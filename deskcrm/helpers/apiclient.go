package helpers

import (
	"deskcrm/conf"
	"git.zuoyebang.cc/fwyybase/fwyylibs/api/apis"
	"git.zuoyebang.cc/fwyybase/fwyylibs/api/zbcore"
	"git.zuoyebang.cc/pkg/golib/v2/base"
)

func InitApiClient() {
	apiClientList := GetApiClientList()
	apis.RegisterApiClient(apiClientList)
	err := zbcore.RegisterClient(GetZbCoreApiClientList())
	if err != nil {
		panic("zbCoreClient register error: %v" + err.Error())
	}
}

func GetApiClientList() []apis.ApiClientInfo {
	return []apis.ApiClientInfo{
		{
			Roots:  apis.KpStaffRoots,
			Client: conf.API.KpStaff,
		},
		{
			Roots:  apis.SuRoots,
			Client: conf.API.Su,
		},
		{
			Roots:  apis.Tower,
			Client: conf.API.Tower,
		},
		{
			Roots:  apis.Allocate,
			Client: conf.API.Allocate,
		},
		{
			Roots:  apis.ArkGo,
			Client: conf.API.ArkGo,
		},
		{
			Roots:  apis.ExamCore,
			Client: conf.API.ExamCore,
		},
		{
			Roots:  apis.MeshRoots,
			Client: conf.API.Mesh,
		},
		{
			Roots:  apis.Muse,
			Client: conf.API.Muse,
		},
		{
			Roots:  apis.Touchmisgo,
			Client: conf.API.TouchMisGo,
		},
		{
			Roots:  apis.Touchmis,
			Client: conf.API.TouchMis,
		},
		{
			Roots:  apis.Assistantdeskgo,
			Client: conf.API.AssistantDeskGo,
		},
		{
			Roots:  apis.CourseBase,
			Client: conf.API.CourseBase,
		},
		{
			Roots:  apis.KpApiRoots,
			Client: conf.API.KpApi,
		},
	}
}

func GetZbCoreApiClientList() map[string]*base.ApiClient {
	return map[string]*base.ApiClient{
		zbcore.ServiceTypeDal: conf.API.ZbCoreDal,
		zbcore.ServiceTypeDau: conf.API.ZbCoreDau,
		zbcore.ServiceTypeDat: conf.API.ZbCoreDat,
		zbcore.ServiceTypeDas: conf.API.ZbCoreDas,
	}
}

package middleware

import (
	"git.zuoyebang.cc/pkg/golib/v2/env"
	"github.com/gin-gonic/gin"
)

func IsDebug(ctx *gin.Context) {
	ctx.Set("isDebug", false)

	if env.GetRunEnv() == env.RunEnvTips || env.GetRunEnv() == env.RunEnvTest {
		//tips环境的, 测试环境
		ctx.Set("isDebug", true)
		return
	}

	isDebug, _ := ctx.<PERSON>ie("__debug__")
	if len(isDebug) > 0 {
		ctx.Set("isDebug", true)
	}

	isDebug, _ = ctx.GetQuery("__debug__")
	if len(isDebug) > 0 {
		ctx.Set("isDebug", true)
	}
	ctx.Next()
}

package keepDetail

// 主要响应结构体
type CourseSchemaResponse map[int64]CourseSchema // key 课程 id

type CourseSchemaDaoStruct struct {
	CourseRecordSchema CourseSchema `json:"courseRecordSchema"`
}

// 课程模式配置
type CourseSchema struct {
	Shoulders    []string              `json:"shoulders"`
	ShoulderTags []string              `json:"shoulderTags"`
	TabData      map[string]TabHeaders `json:"tabData"` // key 枚举 consts/performanceV1.go:7
	SchemaID     int                   `json:"schemaId"`
}

// 标签页表头配置
type TabHeaders struct {
	Headers []Header `json:"headers"`
}

// 表头配置
type Header struct {
	Value      string `json:"value"`
	Label      string `json:"label"`
	Width      string `json:"width"`
	Hover      string `json:"hover"`
	Remark     string `json:"remark"`
	CustomName string `json:"customName"`
}

package lessonList

type LessonRuleConfigStru struct {
	Key        string
	Lable      string
	Function   string
	Sort       int
	FusingRule FusingRuleStru
}
type FusingRuleStru struct {
	TimeoutWarning int64 `json:"timeoutWarning"` //熔断报警墙 单位：ms
	TimeoutFusing  int64 `json:"timeoutFusing"`  //熔断墙 单位：ms
	FusingDuration int64 `json:"fusingDuration"` //熔断时长 单位：s
	Duration       int64 `json:"duration"`       //持续时间 单位：s
	DurationTimes  int64 `json:"durationTimes"`  //持续次数
}

var RuleList []*LessonRuleConfigStru = []*LessonRuleConfigStru{
	{
		Key:        "preview",
		Function:   "GetPreview",
		Lable:      "获取预习数据",
		Sort:       8,
		FusingRule: FusingRuleStru{},
	},
	{
		Key:        "attend",
		Function:   "GetAttendData",
		Lable:      "获取到课数据",
		Sort:       9,
		FusingRule: FusingRuleStru{},
	},
	{
		Key:        "playback",
		Function:   "GetPlayback",
		Lable:      "获取录播数据",
		Sort:       10,
		FusingRule: FusingRuleStru{},
	},
	{
		Key:        "playbackv1",
		Function:   "GetPlaybackOnlineTimeV1",
		Lable:      "获取录播在线时长V1",
		Sort:       11,
		FusingRule: FusingRuleStru{},
	},
	{
		Key:        "lbpAttendDuration",
		Function:   "GetLbpAttendDuration",
		Lable:      "获取LBP到课时长",
		Sort:       12,
		FusingRule: FusingRuleStru{},
	},
	{
		Key:        "lbpAttendDurationOld",
		Function:   "GetLbpAttendDurationOld",
		Lable:      "获取LBP到课时长(旧版)",
		Sort:       13,
		FusingRule: FusingRuleStru{},
	},
	{
		Key:        "inclassTest",
		Function:   "GetInclassTest",
		Lable:      "获取课堂测试",
		Sort:       14,
		FusingRule: FusingRuleStru{},
	},
	{
		Key:        "oralQuestion",
		Function:   "GetOralQuestion",
		Lable:      "获取口语问答",
		Sort:       15,
		FusingRule: FusingRuleStru{},
	},
	{
		Key:        "homework",
		Function:   "GetHomeworkData",
		Lable:      "获取作业数据",
		Sort:       16,
		FusingRule: FusingRuleStru{},
	},
	{
		Key:        "similarHomework",
		Function:   "GetHomeworkLikeData",
		Lable:      "获取相似作业数据",
		Sort:       17,
		FusingRule: FusingRuleStru{},
	},
	{
		Key:        "exercise",
		Function:   "GetExerciseColumn",
		Lable:      "获取练习数据",
		Sort:       18,
		FusingRule: FusingRuleStru{},
	},
	{
		Key:        "exerciseAll",
		Function:   "GetExerciseAllColumn",
		Lable:      "获取全部练习数据",
		Sort:       19,
		FusingRule: FusingRuleStru{},
	},
	{
		Key:        "lbpInteractExam",
		Function:   "GetLbpInteractExamColumn",
		Lable:      "获取LBP互动考试",
		Sort:       20,
		FusingRule: FusingRuleStru{},
	},
	{
		Key:        "mixPlaybackInteract",
		Function:   "GetMixPlaybackInteract",
		Lable:      "获取混合录播互动",
		Sort:       21,
		FusingRule: FusingRuleStru{},
	},
	{
		Key:        "littleKidFudaoHomeworkStatus",
		Function:   "GetLittleKidFudaoData",
		Lable:      "获取小孩辅导作业状态",
		Sort:       22,
		FusingRule: FusingRuleStru{},
	},
	{
		Key:        "littleKidFudaoHomeworkLevel",
		Function:   "GetLittleKidFudaoData",
		Lable:      "获取小孩辅导作业等级",
		Sort:       23,
		FusingRule: FusingRuleStru{},
	},
	{
		Key:        "littleKidFudaoInteract",
		Function:   "GetLittleKidInteractData",
		Lable:      "获取小孩辅导互动",
		Sort:       24,
		FusingRule: FusingRuleStru{},
	},
	{
		Key:        "synchronousPractice",
		Function:   "GetSynchronousPractice",
		Lable:      "获取同步练习",
		Sort:       25,
		FusingRule: FusingRuleStru{},
	},
	{
		Key:        "hasCompositionReport",
		Function:   "GetHasCompositionReportData",
		Lable:      "获取作文报告状态",
		Sort:       26,
		FusingRule: FusingRuleStru{},
	},
	{
		Key:        "talk",
		Function:   "GetTalk",
		Lable:      "获取对话数据",
		Sort:       27,
		FusingRule: FusingRuleStru{},
	},
	{
		Key:        "score",
		Function:   "GetScoreData",
		Lable:      "获取分数数据",
		Sort:       28,
		FusingRule: FusingRuleStru{},
	},
	{
		Key:        "monthlyExamReport",
		Function:   "GetMonthlyExamReportUrl",
		Lable:      "获取月考报告URL",
		Sort:       29,
		FusingRule: FusingRuleStru{},
	},
	{
		Key:        "isInclassTeacherRoomAttend30minute",
		Function:   "GetIsInclassTeacherRoomAttend30minute",
		Lable:      "获取课堂教师房间30分钟到课状态",
		Sort:       30,
		FusingRule: FusingRuleStru{},
	},
	{
		Key:        "isAttendFinish",
		Function:   "GetIsAttendFinish",
		Lable:      "获取到课完成状态",
		Sort:       31,
		FusingRule: FusingRuleStru{},
	},
	{
		Key:        "gjkAttendLessonLubo",
		Function:   "GetGjkAttendLessonLubo",
		Lable:      "获取国际课到课录播状态",
		Sort:       32,
		FusingRule: FusingRuleStru{},
	},
	{
		Key:        "gjkCompleteLessonLubo",
		Function:   "GetGjkCompleteLessonLubo",
		Lable:      "获取国际课完课录播状态",
		Sort:       33,
		FusingRule: FusingRuleStru{},
	},
	{
		Key:        "gjkLessonTag",
		Function:   "GetGjkLessonTag",
		Lable:      "获取国际课章节标签",
		Sort:       34,
		FusingRule: FusingRuleStru{},
	},
	{
		Key:        "lpclessonName",
		Function:   "GetLpcLessonName",
		Lable:      "获取LPC章节名称",
		Sort:       35,
		FusingRule: FusingRuleStru{},
	},
	{
		Key:        "teacherName",
		Function:   "GetLpcTeacherName",
		Lable:      "获取LPC教师名称",
		Sort:       36,
		FusingRule: FusingRuleStru{},
	},
	{
		Key:        "attendStatus",
		Function:   "GetLpcAttendStatus",
		Lable:      "获取LPC到课状态",
		Sort:       37,
		FusingRule: FusingRuleStru{},
	},
	{
		Key:        "finishStatus",
		Function:   "GetLpcFinishStatus",
		Lable:      "获取LPC完课状态",
		Sort:       38,
		FusingRule: FusingRuleStru{},
	},
	{
		Key:        "playStatus",
		Function:   "GetLpcPlayStatus",
		Lable:      "获取LPC播放状态",
		Sort:       39,
		FusingRule: FusingRuleStru{},
	},
	{
		Key:        "preView",
		Function:   "GetLpcPreViewData",
		Lable:      "获取LPC预习数据",
		Sort:       40,
		FusingRule: FusingRuleStru{},
	},
	{
		Key:        "tangtangExamStat",
		Function:   "GetLpcTangTangExamStatData",
		Lable:      "获取LPC堂堂测统计",
		Sort:       41,
		FusingRule: FusingRuleStru{},
	},
	{
		Key:        "strengthPracticeStatus",
		Function:   "GetLpcStrengthPracticeData",
		Lable:      "获取LPC巩固练习状态",
		Sort:       42,
		FusingRule: FusingRuleStru{},
	},
	{
		Key:        "lessonReportUrl",
		Function:   "GetLpcLessonReportData",
		Lable:      "获取LPC章节报告URL",
		Sort:       43,
		FusingRule: FusingRuleStru{},
	},
	{
		Key:        "deerEloquenceHomeworkLevel",
		Function:   "GetDeerEloquenceHomeworkLevel",
		Lable:      "获取小鹿口才作业等级",
		Sort:       44,
		FusingRule: FusingRuleStru{},
	},
	{
		Key:        "deerProgrammingHomeworkLevel",
		Function:   "GetDeerProgrammingHomeworkLevel",
		Lable:      "获取小鹿编程作业等级",
		Sort:       45,
		FusingRule: FusingRuleStru{},
	},
	{
		Key:        "deerLessonReportUrl",
		Function:   "GetDeerLessonReport",
		Lable:      "获取小鹿章节报告URL",
		Sort:       46,
		FusingRule: FusingRuleStru{},
	},
	{
		Key:        "deerLessonHomeWork",
		Function:   "GetLessonHomeWork",
		Lable:      "获取小鹿章节作业",
		Sort:       47,
		FusingRule: FusingRuleStru{},
	},
	{
		Key:        "zhiboLessonReportUrl",
		Function:   "GetZhiboLessonReport",
		Lable:      "获取直播章节报告URL",
		Sort:       48,
		FusingRule: FusingRuleStru{},
	},
}

func GetRuleMap() map[string]*LessonRuleConfigStru {
	ruleMap := map[string]*LessonRuleConfigStru{}
	for idx := range RuleList {
		ruleDetail := RuleList[idx]
		ruleMap[ruleDetail.Key] = ruleDetail
	}
	return ruleMap
}

package keepDetail

import (
	"deskcrm/api/dal"
)

// BasicData 基础数据结构
type BasicData struct {
	CourseId           int64          // 课程ID
	LessonIds          []int64        // 章节ID列表
	StudentId          int64          // 学生ID
	CourseLessonInfos  dal.CourseInfo // 课程章节信息
	CourseRecordConfig CourseSchema   // 课程记录配置
	Tab                string         // 当前tab
	FuncFieldKeys      []string       // 函数字段key
	ArkFieldKeys       []string       // 方舟字段key
	AssistantUid       int64          // 辅导老师ID
	PersonUid          int64          // 真人ID
	LeadsId            int64          // 线索ID
}

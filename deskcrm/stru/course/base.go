package course

type SurveyTypeAndId struct {
	NeedSurveyId  int64   `json:"needSurveyId"`
	OrderSurveyId int64   `json:"orderSurveyId"`
	SurveyType    []int64 `json:"surveyType"`
}

type NextLastLessonStruct struct {
	NextLessonInfo LessonPlayStruct `json:"nextLessonInfo"`
	LastLessonInfo LessonPlayStruct `json:"lastLessonInfo"`
}

type LessonPlayStruct struct {
	StartTime int64 `json:"startTime"`
	StopTime  int64 `json:"stopTime"`
	LessonId  int64 `json:"lessonId"`
}

type AllowAutoCallAndMessageResp struct {
	Allow int `json:"allow"`
}

type GetSipInfoResp struct {
	AgentId string `json:"agentId"`
	Pwd     string `json:"pwd"`
	URL     string `json:"url"`
}

type AssistantNameInfo struct {
	StaffNames  []string // 员工名字列表
	DeviceNames []string // 设备名字列表
}

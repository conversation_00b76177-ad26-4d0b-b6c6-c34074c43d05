package models

import (
	"deskcrm/consts"
	"deskcrm/helpers"
	"github.com/gin-gonic/gin"
	"gorm.io/gorm"
)

var ArkTemplateCourseBindDao arkTemplateCourseBindDao

type arkTemplateCourseBindDao struct {
}

// ArkTemplateCourseBind 课程-模板绑定关系表
type ArkTemplateCourseBind struct {
	ID          int64  `gorm:"primary_key;column:id" json:"id"`
	TplId       int64  `gorm:"column:tpl_id" json:"tplId"`
	CourseId    int64  `gorm:"column:course_id" json:"courseId"`
	Status      int    `gorm:"column:status" json:"status"`
	OperatorUid int64  `gorm:"column:operator_uid" json:"operatorUid"`
	Operator    string `gorm:"column:operator" json:"operator"`
	CreateTime  int64  `gorm:"column:create_time" json:"createTime"`
	UpdateTime  int64  `gorm:"column:update_time" json:"updateTime"`
}

func (ArkTemplateCourseBind) TableName() string {
	return TblArkTemplateCourseBind
}

func (dao *arkTemplateCourseBindDao) GetArkTemplateBindsByCourseIds(ctx *gin.Context, courseId []int64, tx *gorm.DB) (res []*ArkTemplateCourseBind, err error) {
	if tx == nil {
		tx = helpers.MysqlClient
	}

	conditions := map[string]interface{}{
		"course_id": courseId,
		"status":    consts.STATUS_BIND,
	}

	err = tx.WithContext(ctx).Where(conditions).Find(&res).Error
	if err != nil {
		return nil, err
	}
	return res, nil
}

func (dao *arkTemplateCourseBindDao) GetArkTemplateBindByTplIds(ctx *gin.Context, tplIds []int64, tx *gorm.DB) (arkTemplateCourseBindList []*ArkTemplateCourseBind, err error) {
	if tx == nil {
		tx = helpers.MysqlClient
	}

	arkTemplateCourseBindList = make([]*ArkTemplateCourseBind, 0)
	err = tx.WithContext(ctx).WithContext(ctx).
		Where("tpl_id in (?) and status = ?", tplIds, ARKTEMPLATE_SERVICE_FIELD_STATUS_VALID).
		Order("update_time desc").
		Limit(100).
		Find(&arkTemplateCourseBindList).Error
	if err != nil {
		return
	}
	return
}

package inputArk

import (
	"deskcrm/service/arkBase/arkConfig"
	"github.com/gin-gonic/gin"
	jsoniter "github.com/json-iterator/go"
)

type ArkListParam struct {
	AssistantUid     int64  `json:"assistantUid" form:"assistantUid" binding:"required"`
	PersonUid        int64  `json:"personUid" form:"personUid" binding:"required"`
	CourseId         int64  `json:"courseId" form:"courseId" binding:"required"`
	LessonId         int64  `json:"lessonId" form:"lessonId"`
	TaskId           int64  `json:"taskId" form:"taskId"`
	TplId            int64  `json:"tplId" form:"tplId"`
	ServiceId        int64  `json:"serviceId" form:"serviceId"`
	Timestamp        int64  `json:"timestamp" form:"timestamp"`
	Keyword          string `json:"keyword" form:"keyword"`
	Sorts            string `json:"sorts" form:"sorts"`
	Filter           string `json:"filter" form:"filter"`
	DataRangeSelect  string `json:"dataRangeSelect" form:"dataRangeSelect"`
	NeedFieldKeysOri string `json:"needFieldKeys" form:"needFieldKeys"`
	NeedFieldKeys    []string
	Offset           int64 `json:"offset" form:"offset"`
	Limit            int64 `json:"limit" form:"limit"`
}

func (c *ArkListParam) Validate(ctx *gin.Context) error {
	if c.TplId == 0 {
		bindDetail, err := arkConfig.GetTemplateConfigInstance(ctx).GetArkTemplateBindsByCourseId(ctx, int(c.CourseId))
		if err != nil {
			return err
		}
		c.TplId = bindDetail.TplId
	}
	if c.Limit == 0 {
		c.Limit = 20
	}

	if len(c.NeedFieldKeysOri) > 0 {
		_ = jsoniter.UnmarshalFromString(c.NeedFieldKeysOri, &c.NeedFieldKeys)
	}
	return nil
}

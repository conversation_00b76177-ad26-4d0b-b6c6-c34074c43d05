package inputStudent

import (
	"deskcrm/components"
	"deskcrm/middleware"
	"git.zuoyebang.cc/pkg/golib/v2/base"
	"git.zuoyebang.cc/pkg/golib/v2/zlog"
	"github.com/gin-gonic/gin"
)

type SetBelongerParam struct {
	StudentUid   int64 `json:"studentUid" form:"studentUid" required:"true"`
	AssistantUid int64 `json:"assistantUid" form:"assistantUid"`
	Belonger     int64 `json:"belonger" form:"belonger" required:"true"`
	WeixinId     int64 `json:"weixinId" form:"weixinId" required:"true"`
}

func (c *SetBelongerParam) Validate(ctx *gin.Context) error {
	loginDeviceInfo, err := middleware.GetSelectDeviceInfo(ctx)
	if err != nil {
		zlog.Infof(ctx, "collection loginFail device error, err: %+v", err)
		base.RenderJsonFail(ctx, components.ErrorUserNotLogin)
		return components.ErrorParamInvalid
	}

	c.AssistantUid = loginDeviceInfo.DeviceUid
	return nil
}

package inputStudent

import (
	"deskcrm/components"
	"deskcrm/middleware"
	"git.zuoyebang.cc/pkg/golib/v2/base"
	"git.zuoyebang.cc/pkg/golib/v2/zlog"
	"github.com/gin-gonic/gin"
)

// request参数
type StudentRelationListParam struct {
	StudentUidJson string `form:"studentUidList" json:"studentUidList"`
	AssistantUid   int64  `form:"assistantUid" json:"assistantUid"`
}

func (c *StudentRelationListParam) Validate(ctx *gin.Context) error {
	loginDeviceInfo, err := middleware.GetSelectDeviceInfo(ctx)
	if err != nil {
		zlog.Infof(ctx, "collection loginFail device error, err: %+v", err)
		base.RenderJsonFail(ctx, components.ErrorUserNotLogin)
		return components.ErrorParamInvalid
	}
	c.AssistantUid = loginDeviceInfo.DeviceUid
	return nil
}

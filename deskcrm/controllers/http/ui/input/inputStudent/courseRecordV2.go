package inputStudent

import (
	"deskcrm/api/allocate"
	"deskcrm/components"
	"deskcrm/consts"
	"deskcrm/middleware"
	"fmt"
	"git.zuoyebang.cc/pkg/golib/v2/zlog"
	"github.com/gin-gonic/gin"
	jsoniter "github.com/json-iterator/go"
	"sort"
	"time"
)

type CourseRecordV2Param struct {
	StudentUid        int64  `form:"studentUid" json:"studentUid" binding:"required"` // 学生ID，如4085004376
	CourseId          int64  `form:"courseId" json:"courseId" binding:"required"`     // 课程ID，如3647015
	LeadsId           int64  `form:"leadsId" json:"leadsId"`                          // 例子
	Category          int    `form:"category" json:"category"`                        // 类别，维系详情配置，按照服务人群/按照课程类型/按照学季维度
	CourseServiceType string `form:"courseServiceType" json:"courseServiceType"`      // 课程服务类型，如ZJ/LX/OTHER
	Year              int    `form:"year" json:"year"`                                // 年份，如-1，2025
	Season            int    `form:"season" json:"season"`                            // 季度，-1（全部），1，2，3，4
	BuyType           int    `form:"buyType" json:"buyType"`                          // 购买类型，1，2，3
	OnlyCurrentCourse int    `form:"onlyCurrentCourse" json:"onlyCurrentCourse"`      // 只看当前课程 0，1
	OnlyOnlineCourse  int    `form:"onlyOnlineCourse" json:"onlyOnlineCourse"`        // 只看服务期课程 0，1
	NewCourseTypes    string `form:"newCourseTypes" json:"newCourseTypes"`            // 课程类型筛选
	NewCourseTypesArr []int64
	AssistantUid      int64 `form:"assistantUid" json:"assistantUid"` // 资产 id
	PersonUid         int64 `form:"personUid" json:"personUid"`       // 真人 id

	StartTime int64
	EndTime   int64
}

func (p *CourseRecordV2Param) Validate(ctx *gin.Context) error {
	userInfo, err := middleware.GetLoginUserInfo(ctx)
	if err != nil {
		zlog.Warnf(ctx, "CourseRecordV2Param get user info failed, err: %v", err)
		return components.ErrorUserNotLogin
	}

	deviceInfo, err := middleware.GetSelectDeviceInfo(ctx)
	if err != nil {
		zlog.Warnf(ctx, "CourseRecordV2Param get device info failed, err: %v", err)
		return components.ErrorUserNotLogin
	}

	p.PersonUid = int64(userInfo.UserId)
	p.AssistantUid = deviceInfo.DeviceUid

	if p.LeadsId == 0 {
		leadsInfo, err := allocate.NewClient().GetLeadsByBatchCourseIdUid(ctx, []int64{p.CourseId}, p.StudentUid)
		if err != nil {
			zlog.Error(ctx, "GetLeadsByBatchCourseIdUid failed: %v", err)
			return err
		}
		if len(leadsInfo) == 0 {
			zlog.Error(ctx, "GetLeadsByBatchCourseIdUid empty: %v", err)
			return components.ErrorParamInvalid
		}
		sort.Slice(leadsInfo, func(i, j int) bool {
			return leadsInfo[i].AllocTime > leadsInfo[j].AllocTime
		})
		p.LeadsId = leadsInfo[0].LeadsId
	}

	if p.Year == 0 {
		p.Year = consts.YearAll
	}

	if p.Season == 0 {
		p.Season = consts.SeasonAll
	}

	if p.BuyType == 0 {
		p.BuyType = consts.BuyAll
	}

	if len(p.NewCourseTypes) > 0 {
		err = jsoniter.UnmarshalFromString(p.NewCourseTypes, &p.NewCourseTypesArr)
		if err != nil {
			zlog.Error(ctx, "UnmarshalFromString err: %v", err)
			return err
		}
	}

	if p.Year == consts.YearAll {
		now := time.Now().Unix()
		p.StartTime = now - 365*86400
		p.EndTime = now
	} else {
		timeStr := fmt.Sprintf("%d-01-01", p.Year)
		yearStart, _ := time.Parse("2006-01-02", timeStr)
		p.StartTime = yearStart.Unix() - 180*86400
		p.EndTime = yearStart.Unix() + 365*86400
	}

	return nil
}

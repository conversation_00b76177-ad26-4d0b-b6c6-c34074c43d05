package inputStudent

import (
	"deskcrm/api/allocate"
	"deskcrm/components"
	"deskcrm/middleware"
	"git.zuoyebang.cc/pkg/golib/v2/zlog"
	"github.com/gin-gonic/gin"
	"sort"
	"strings"
)

type StudentDetailV1Param struct {
	StudentUid   int64  `form:"studentUid" json:"studentUid" binding:"required"`
	CourseId     int64  `form:"courseId" json:"courseId" binding:"required"`
	LeadsId      int64  `form:"leadsId" json:"leadsId"`
	AssistantUid int64  `form:"assistantUid" json:"assistantUid"`
	PersonUid    int64  `form:"personUid" json:"personUid"`
	Tags         string `form:"tags" json:"tags"`
	TagArr       []string
}

func (p *StudentDetailV1Param) Validate(ctx *gin.Context) error {
	userInfo, err := middleware.GetLoginUserInfo(ctx)
	if err != nil {
		zlog.Warnf(ctx, "StudentDetailV1Param get user info failed, err: %v", err)
		return components.ErrorUserNotLogin
	}

	deviceInfo, err := middleware.GetSelectDeviceInfo(ctx)
	if err != nil {
		zlog.Warnf(ctx, "StudentDetailV1Param get device info failed, err: %v", err)
		return components.ErrorUserNotLogin
	}

	p.PersonUid = int64(userInfo.UserId)
	p.AssistantUid = deviceInfo.DeviceUid

	if p.Tags != "" {
		p.TagArr = strings.Split(p.Tags, ",")
	}

	if p.LeadsId == 0 {
		leadsInfo, err := allocate.NewClient().GetLeadsByBatchCourseIdUid(ctx, []int64{p.CourseId}, p.StudentUid)
		if err != nil {
			zlog.Error(ctx, "GetLeadsByBatchCourseIdUid failed: %v", err)
			return err
		}
		if len(leadsInfo) == 0 {
			zlog.Error(ctx, "GetLeadsByBatchCourseIdUid empty: %v", err)
			return components.ErrorParamInvalid
		}
		sort.Slice(leadsInfo, func(i, j int) bool {
			return leadsInfo[i].AllocTime > leadsInfo[j].AllocTime
		})
		p.LeadsId = leadsInfo[0].LeadsId
	}

	return nil
}

package outputStudent

// PerformanceV1Output 学生课程表现数据响应结构
type PerformanceV1Output struct {
	SchemaId    string            `json:"schemaId"`    // 配置模式ID
	TableData   []LessonTableRow  `json:"tableData"`   // 章节数据数组
	TableHeader []TableHeaderItem `json:"tableHeader"` // 表头配置数组
}

type LessonTableRow map[string]interface{}

// TableHeaderItem 表头配置项结构
type TableHeaderItem struct {
	Label string `json:"label"` // 显示标签
	Prop  string `json:"prop"`  // 字段名
	Cname string `json:"cname"` // 组件名称
	Width int    `json:"width"` // 列宽
	Hover string `json:"hover"` // 悬停提示
	Sort  int    `json:"sort"`  // 排序
}

// ExamTestListOutput 试卷测试列表输出结构
type ExamTestListOutput struct {
	TableData   []map[string]interface{} `json:"tableData"`   // 试卷数据
	TableHeader []TableHeaderItem        `json:"tableHeader"` // 试卷表头
}

package innerapi

import (
	"github.com/gin-gonic/gin"
)

func RegisterHandlers(rg *gin.RouterGroup) {
	innerapi := rg.Group("/innerapi")
	apiRouter := rg.Group("/api")
	// ------------------------------------------------api router-------------------------------------------------------
	courseApiGroup := apiRouter.Group("/course/")
	{
		courseApiGroup.POST("courselistandcardbyyear", CourseController.CourseListAndCardByYearAPI)
		courseApiGroup.POST("getLessonplayinfolist", CourseController.GetLessonPlayInfoList)
		courseApiGroup.GET("allowautocallandmessage", CourseController.AllowAutoCallAndMessage)
	}

	apiFilterGroup := apiRouter.Group("/filter/")
	{
		apiFilterGroup.POST("getfilterconfig", ArkController.GetFilterConfigAPI)
		apiFilterGroup.GET("getfilterconfig", ArkController.GetFilterConfigAPI)
	}

	apiTaskGroup := apiRouter.Group("/task/")
	{
		apiTaskGroup.POST("lessoninfo", LessonController.GetLessonListAPI)
	}
	//个人信息
	studentGroup := innerapi.Group("/student")
	{
		studentGroup.GET("getstudentrelationlist", StudentController.GetStudentRelationList)
		studentGroup.POST("/setbelonger", StudentController.SetBelonger)
		studentGroup.GET("/getbelongermap", StudentController.GetBelongerMap)
		studentGroup.GET("/getbelongerlist", StudentController.GetBelongerList)
	}

	apiDeerGroup := apiRouter.Group("/deer/")
	{
		apiDeerGroup.POST("getlistbystudentlessons", DeerController.GetListByStudentLessonsAPI)
	}

	toolGroup := apiRouter.Group("/tool/")
	{
		toolGroup.GET("startTask", ToolController.StartUpdateHandler)
		toolGroup.GET("stopTask", ToolController.StopUpdateHandler)
	}

	keepDetailGroup := innerapi.Group("/keepdetail/")
	{
		keepDetailGroup.GET("lessonlist", KeepDetailController.LessonList)
	}
}

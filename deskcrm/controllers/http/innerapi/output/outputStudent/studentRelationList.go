package outputStudent

type StudentRelationListOutput []StudentRelationList

type StudentRelationList struct {
	AssistantUID   int64  `json:"assistantUid"`
	StudentUid     int64  `json:"studentUid"`
	Remark         string `json:"remark"`
	WeixinName     string `json:"weixinName"`
	WeixinNickName string `json:"weixinNickName"`
	CreateTime     int64  `json:"createTime"`
	RemoteId       int64  `json:"remoteId"`
	ExternalUserId string `json:"externalUserId"`
	Belonger       int64  `json:"belonger"`
	BelongerName   string `json:"belongerName"`
}

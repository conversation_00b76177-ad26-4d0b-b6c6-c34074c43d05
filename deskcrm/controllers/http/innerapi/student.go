package innerapi

import (
	"deskcrm/components"
	"deskcrm/controllers/http/innerapi/input/inputStudent"
	"deskcrm/service/ui"
	"errors"
	"git.zuoyebang.cc/pkg/golib/v2/base"
	"git.zuoyebang.cc/pkg/golib/v2/zlog"
	"github.com/gin-gonic/gin"
	json "github.com/json-iterator/go"
)

var StudentController studentController

type studentController struct {
}

func (r studentController) GetStudentRelationList(ctx *gin.Context) {
	var param inputStudent.StudentRelationListParam
	if err := ctx.ShouldBind(&param); err != nil {
		base.RenderJsonFail(ctx, components.InvalidParam(err.Error()))
		return
	}
	err := param.Validate(ctx)
	if err != nil {
		base.RenderJsonFail(ctx, components.InvalidParam(err.<PERSON>rror()))
		return
	}
	paramJson, _ := json.MarshalToString(param)
	zlog.Infof(ctx, "request param: %s", paramJson)

	studentUIDs := make([]int64, 0)
	if len(param.StudentUidJson) > 0 {
		_ = json.UnmarshalFromString(param.StudentUidJson, &studentUIDs)
	}

	// service
	rsp, err := ui.StudentService.GetStudentRelationList(ctx, studentUIDs, param.AssistantUid)
	if err != nil {
		base.RenderJsonFail(ctx, errors.New(err.Error()))
		return
	}

	base.RenderJsonSucc(ctx, rsp)
	return
}

func (r studentController) SetBelonger(ctx *gin.Context) {
	params := inputStudent.SetBelongerParam{}
	if err := ctx.ShouldBind(&params); err != nil {
		zlog.Error(ctx, "[SetBelonger] params error: ", err.Error())
		base.RenderJsonFail(ctx, err)
		return
	}

	resp, err := ui.StudentService.SetBelonger(ctx, params.AssistantUid, params.StudentUid, params.WeixinId, params.Belonger)
	if err != nil {
		zlog.Error(ctx, "[SetBelonger] cancel error: ", err.Error())
		base.RenderJsonFail(ctx, err)
		return
	}

	base.RenderJsonSucc(ctx, resp)
	return
}

func (r studentController) GetBelongerMap(ctx *gin.Context) {
	resp, err := ui.StudentService.GetBelongerMap(ctx)
	if err != nil {
		zlog.Error(ctx, "[GetBelongerMap] cancel error: ", err.Error())
		base.RenderJsonFail(ctx, err)
		return
	}

	base.RenderJsonSucc(ctx, resp)
	return
}

func (r studentController) GetBelongerList(ctx *gin.Context) {
	var param inputStudent.GetBelongerListParam
	if err := ctx.ShouldBind(&param); err != nil {
		base.RenderJsonFail(ctx, components.InvalidParam(err.Error()))
		return
	}
	err := param.Validate(ctx)
	if err != nil {
		base.RenderJsonFail(ctx, components.InvalidParam(err.Error()))
		return
	}
	paramJson, _ := json.MarshalToString(param)
	zlog.Infof(ctx, "request param: %s", paramJson)

	studentUIDs := make([]int64, 0)
	if len(param.StudentUidJson) > 0 {
		_ = json.UnmarshalFromString(param.StudentUidJson, &studentUIDs)
	}

	// service
	rsp, err := ui.StudentService.GetBelongerList(ctx, studentUIDs, param.AssistantUid)
	if err != nil {
		base.RenderJsonFail(ctx, errors.New(err.Error()))
		return
	}

	base.RenderJsonSucc(ctx, rsp)
	return
}

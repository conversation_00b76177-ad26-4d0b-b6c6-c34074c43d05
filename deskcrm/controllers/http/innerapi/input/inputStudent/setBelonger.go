package inputStudent

import (
	"github.com/gin-gonic/gin"
)

type SetBelongerParam struct {
	StudentUid   int64 `json:"studentUid" form:"studentUid" required:"true"`
	AssistantUid int64 `json:"assistantUid" form:"assistantUid"`
	Belonger     int64 `json:"belonger" form:"belonger" required:"true"`
	WeixinId     int64 `json:"weixinId" form:"weixinId" required:"true"`
}

func (c *SetBelongerParam) Validate(ctx *gin.Context) error {
	return nil
}

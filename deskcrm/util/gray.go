package util

import (
	"deskcrm/api/mercury"
	"deskcrm/models"
	"git.zuoyebang.cc/fwyybase/fwyylibs/fwyyutils"
	"git.zuoyebang.cc/pkg/golib/v2/zlog"
	"github.com/gin-gonic/gin"
	json "github.com/json-iterator/go"
)

const (
	HideSensitivePhoneMercuryKey = "deskcrm_hide_sensitive_phone_key"
)

type grayConfigConfig struct {
	Disable      bool    `json:"disable"`
	StaffId      []int64 `json:"staffId"`
	BlackStaffId []int64 `json:"blackStaffId"`
	Percent      int64   `json:"percent"` // 0-100
}

// IsHitGray 支持按照真人配置黑白名单，以及百分比灰度
func IsHitGray(ctx *gin.Context, personUid int64, key string) bool {
	var mercuryConfig grayConfigConfig
	err := mercury.NewClient().GetConfigForJson(ctx, key, GRAY_DEFAULT_TTL, &mercuryConfig)
	if err != nil {
		zlog.Warnf(ctx, "HitGray GetConfigForJson fai, key=%v,err=%v", key, err)
		return false
	}
	if mercuryConfig.Disable {
		return false
	}

	if hit, _ := fwyyutils.InArrayInt64(personUid, mercuryConfig.BlackStaffId); hit {
		return false
	}

	if hit, _ := fwyyutils.InArrayInt64(personUid, mercuryConfig.StaffId); hit {
		return true
	}

	if personUid%100 <= mercuryConfig.Percent {
		return true
	}

	return false
}

func GetHidePriceConfigs(ctx *gin.Context) []int64 {
	config, err := models.ConfigDao.GetConfigByKey(ctx, "courseRecordHidePriceByTemplateID")
	if err != nil || config == nil {
		return []int64{1381}
	}
	var configs []int64
	err = json.Unmarshal([]byte(config.CfgValue), &configs)
	if err != nil {
		return []int64{1381}
	}
	return configs
}

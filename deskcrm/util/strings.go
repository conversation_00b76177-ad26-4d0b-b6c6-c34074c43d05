package util

import (
	"strconv"
	"strings"
)

func GetStringValue(m map[string]interface{}, key string) string {
	if v, ok := m[key].(string); ok {
		return v
	}
	return ""
}

// 将整型类型的切片转换为string切片
func IntegerList2StringList[T int | int8 | int16 | int32 | int64 | uint | uint8 | uint16 | uint32 | uint64](intList []T) []string {
	result := make([]string, len(intList))
	for i, id := range intList {
		switch any(id).(type) {
		case int:
			result[i] = strconv.Itoa(int(id))
		case int8:
			result[i] = strconv.Itoa(int(id))
		case int16:
			result[i] = strconv.Itoa(int(id))
		case int32:
			result[i] = strconv.Itoa(int(id))
		case int64:
			result[i] = strconv.FormatInt(int64(id), 10)
		case uint:
			result[i] = strconv.FormatUint(uint64(id), 10)
		case uint8:
			result[i] = strconv.FormatUint(uint64(id), 10)
		case uint16:
			result[i] = strconv.FormatUint(uint64(id), 10)
		case uint32:
			result[i] = strconv.FormatUint(uint64(id), 10)
		case uint64:
			result[i] = strconv.FormatUint(uint64(id), 10)
		}
	}
	return result
}

func GetByDefault(s string) string {
	if s == "" {
		return "-"
	}
	return s
}

func ContainsInt64InString(str string, num int64) bool {
	if str == "" {
		return false
	}

	numStr := strconv.FormatInt(num, 10)
	strList := strings.Split(str, ",")

	for _, id := range strList {
		if strings.TrimSpace(id) == numStr {
			return true
		}
	}
	return false
}

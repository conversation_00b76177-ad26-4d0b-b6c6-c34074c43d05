package arkConfig

import (
	"deskcrm/models"
	"deskcrm/service/arkBase/arkConfig/arkTemplate"
	"github.com/gin-gonic/gin"
)

type TemplateConfigManager interface {
	GetArkTemplateBindsByCourseId(ctx *gin.Context, courseId int) (*models.ArkTemplateCourseBind, error)
	GetArkTemplateBindsByCourseIds(ctx *gin.Context, courseIds []int64) (map[int64]*models.ArkTemplateCourseBind, error)
	GetServicesTplRelationByTplId(ctx *gin.Context, tplId int) ([]*models.ArkTemplateServiceRelation, error)
	GetFieldsByTplAndService(ctx *gin.Context, tplId, serviceId, assistantUid int64, forPreData bool, personUid int64) ([]*models.ArkTemplateServiceField, error)
	GetFieldListByTplId(ctx *gin.Context, tplId int64) ([]*models.ArkTemplateServiceField, error)
	GetTargetByTpl(ctx *gin.Context, tplId int64) ([]*models.ArkTemplateServiceTarget, error)
	GetOverviewByTpl(ctx *gin.Context, tplId int64) ([]*models.ArkTemplateServiceOverview, error)
	GetFeatureByTpl(ctx *gin.Context, tplId int64) ([]*models.ArkTemplateServiceFeature, error)
}

func GetTemplateConfigInstance(ctx *gin.Context) TemplateConfigManager {
	return &arkTemplate.TemplateImpl{}
}

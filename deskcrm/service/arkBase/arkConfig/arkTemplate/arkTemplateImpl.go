package arkTemplate

import (
	"deskcrm/api/assistantdeskgo"
	"deskcrm/components"
	"deskcrm/consts"
	"deskcrm/models"
	"deskcrm/stru/template"
	"deskcrm/util"
	"git.zuoyebang.cc/fwyybase/fwyylibs/fwyyutils"
	"github.com/gin-gonic/gin"
	json "github.com/json-iterator/go"
	"github.com/spf13/cast"
)

type TemplateImpl struct{}

func (s *TemplateImpl) GetFeatureByTpl(ctx *gin.Context, tplId int64) (services []*models.ArkTemplateServiceFeature, err error) {
	services = make([]*models.ArkTemplateServiceFeature, 0)

	// 目前模板 tab 删除后，之前相关的绑定信息不会删除，这里按模板查询的场景都加上有效的 serviceId 参数
	serviceIds, err := getServiceIdsByTplId(ctx, tplId)
	if err != nil {
		return
	}
	services, err3 := models.ArkTemplateServiceFeatureDao.GetFeatureByTplId(ctx, tplId, serviceIds, nil)
	if err3 != nil {
		return nil, err
	}

	return services, err
}

func (s *TemplateImpl) GetTargetByTpl(ctx *gin.Context, tplId int64) (services []*models.ArkTemplateServiceTarget, err error) {
	services = make([]*models.ArkTemplateServiceTarget, 0)

	// 目前模板 tab 删除后，之前相关的绑定信息不会删除，这里按模板查询的场景都加上有效的 serviceId 参数
	serviceIds, err := getServiceIdsByTplId(ctx, tplId)
	if err != nil {
		return
	}
	// 读 db
	services, err3 := models.ArkTemplateServiceTargetDao.GetTargetByTpl(ctx, tplId, serviceIds, nil)
	if err3 != nil {
		return nil, err
	}

	return services, err
}

func (s *TemplateImpl) GetOverviewByTpl(ctx *gin.Context, tplId int64) (services []*models.ArkTemplateServiceOverview, err error) {

	services = make([]*models.ArkTemplateServiceOverview, 0)
	// 目前模板 tab 删除后，之前相关的绑定信息不会删除，这里按模板查询的场景都加上有效的 serviceId 参数
	serviceIds, err := getServiceIdsByTplId(ctx, tplId)
	if err != nil {
		return
	}
	// 读 db
	services, err3 := models.ArkTemplateServiceOverviewDao.GetOverViewByTpl(ctx, tplId, serviceIds, nil)
	if err3 != nil {
		return nil, err
	}

	return services, err
}

func (s *TemplateImpl) GetServicesTplRelationByTplId(ctx *gin.Context, tplId int) ([]*models.ArkTemplateServiceRelation, error) {
	services, err := models.ArkTemplateServiceRelationDao.GetByTplID(ctx, []int{tplId})
	if err != nil {
		return nil, err
	}

	return services, err
}

func (s *TemplateImpl) GetArkTemplateBindsByCourseId(ctx *gin.Context, courseId int) (*models.ArkTemplateCourseBind, error) {
	tplBindMap, err := s.GetArkTemplateBindsByCourseIds(ctx, []int64{int64(courseId)})
	if err != nil {
		return nil, err
	}
	if _, ok := tplBindMap[int64(courseId)]; !ok {
		return nil, fwyyutils.ErrMsg.Warnf(ctx, "课程未绑定方舟模板，请联系运营人员进行绑定！, courseId:%d", courseId)
	}
	return tplBindMap[int64(courseId)], nil
}

func (s *TemplateImpl) GetArkTemplateBindsByCourseIds(ctx *gin.Context, courseId []int64) (map[int64]*models.ArkTemplateCourseBind, error) {
	templates, err := models.ArkTemplateCourseBindDao.GetArkTemplateBindsByCourseIds(ctx, courseId, nil)
	if err != nil {
		return nil, err
	}

	templateMap := make(map[int64]*models.ArkTemplateCourseBind, len(templates))
	for _, templateItem := range templates {
		templateMap[templateItem.CourseId] = templateItem
	}

	return templateMap, nil
}

func convertToolToFront(old template.TeacherStyleDao) template.TeacherStyle {
	res := template.TeacherStyle{
		IsSelf:         old.IsSelf,
		ServiceType:    old.ServiceType,
		IsHide:         old.IsHide,
		Height:         old.Height,
		Tools:          nil,
		Collection:     old.Collection,
		FieldMapTree:   old.FieldMapTree,
		FieldStyleInfo: old.FieldStyleInfo,
	}

	tool := make([]template.ToolsStyleForFront, 0)
	for _, item := range old.Tools {
		tool = append(tool, template.ToolsStyleForFront{
			Group:  item.Group,
			Key:    cast.ToInt64(item.Key),
			IsHide: item.IsHide,
			Name:   item.Name,
		})
	}
	res.Tools = tool
	return res
}

func (s *TemplateImpl) GetFieldsByTplAndService(ctx *gin.Context, tplId, serviceId, assistantUid int64, forPreData bool, personUid int64) ([]*models.ArkTemplateServiceField, error) {
	// 模板信息
	arkTemplateServiceFieldList, baseTmplServiceFieldsMap, allArkTemplateFieldMap, err := getFieldsInfoByTpl(ctx, tplId, serviceId)
	if err != nil {
		return nil, err
	}

	// 运营预览
	if forPreData {
		return handleDefaultFields(ctx, personUid, tplId, serviceId, arkTemplateServiceFieldList)
	}
	// 老师环节
	var arkTeacherStyleTmplOld *template.TeacherStyleDao
	arkTeacherStyleTemplates, err := models.ArkTemplatePersonStyleDao.GetArkTemplateByServiceIdAndAssistantUid(ctx, tplId, assistantUid, []int64{serviceId}, nil)
	if err != nil {
		return nil, err
	}

	if len(arkTeacherStyleTemplates) == 1 && arkTeacherStyleTemplates[0].Style != "" {
		err = json.UnmarshalFromString(arkTeacherStyleTemplates[0].Style, &arkTeacherStyleTmplOld)
		if err != nil {
			return nil, err
		}

		arkTeacherStyleTmpl := convertToolToFront(*arkTeacherStyleTmplOld)

		for _, key := range arkTeacherStyleTmpl.FieldMapTree {
			// 字段不在模板上了
			if _, ok := allArkTemplateFieldMap[key]; !ok {
				continue
			}
			// 原来的字段不加
			if _, ok := baseTmplServiceFieldsMap[key]; ok {
				continue
			}
			// 不返回隐藏的字段
			if arkTeacherStyleTmpl.FieldStyleInfo[key].IsHide == components.ArkFieldMapHide {
				continue
			}

			arkTemplateServiceFieldList = append(arkTemplateServiceFieldList, &models.ArkTemplateServiceField{
				ID:         0,
				TplId:      tplId,
				ServiceId:  serviceId,
				FieldKey:   key,
				FieldName:  allArkTemplateFieldMap[key].FieldName,
				FieldHover: allArkTemplateFieldMap[key].FieldHover,
				FieldHide:  0,
				Status:     0,
				CreateTime: 0,
				UpdateTime: 0,
			})
		}
	}
	return handleDefaultFields(ctx, personUid, tplId, serviceId, arkTemplateServiceFieldList)
}

func (s *TemplateImpl) GetFieldListByTplId(ctx *gin.Context, tplId int64) ([]*models.ArkTemplateServiceField, error) {
	// 模板信息
	return getFieldsInfoByTplId(ctx, tplId, 0)
}

func handleDefaultFields(ctx *gin.Context, personUid, tplId, serviceId int64, arkTemplateServiceFieldList []*models.ArkTemplateServiceField) ([]*models.ArkTemplateServiceField, error) {
	// 灰度内屏蔽部分老字段,默认加上新版学生信息/操作
	_, config := util.GetBlackArkConfig(ctx)
	blackFieldKey := config.OldBlackFields
	blackNewFieldKey := config.NewBlackFields

	hit, err := assistantdeskgo.NewClient().GrayHit(ctx, personUid, consts.RWLBYHGrayKey)
	if err == nil && hit {
		res := make([]*models.ArkTemplateServiceField, 0)
		res = append(res, &models.ArkTemplateServiceField{
			ID:         0,
			TplId:      tplId,
			ServiceId:  serviceId,
			FieldKey:   components.FieldKeyStudentInfoNewV3,
			FieldName:  "学员信息(新)",
			FieldHover: "",
			FieldHide:  0,
			Status:     0,
			CreateTime: 0,
			UpdateTime: 0,
		})

		for _, item := range arkTemplateServiceFieldList {
			if item.FieldKey == components.FieldKeyStudentInfoNewV3 || item.FieldKey == components.FieldKeyKeepDetail {
				continue
			}
			if _, ok := blackFieldKey[item.FieldKey]; ok {
				continue
			}
			res = append(res, item)
		}

		res = append(res, &models.ArkTemplateServiceField{
			ID:         0,
			TplId:      tplId,
			ServiceId:  serviceId,
			FieldKey:   components.FieldKeyKeepDetail,
			FieldName:  "操作",
			FieldHover: "",
			FieldHide:  0,
			Status:     0,
			CreateTime: 0,
			UpdateTime: 0,
		})
		return res, nil
	} else {
		// 灰度外屏蔽新增的,避免出现两个学生信息字段
		res := make([]*models.ArkTemplateServiceField, 0)
		for _, item := range arkTemplateServiceFieldList {
			if _, ok := blackNewFieldKey[item.FieldKey]; ok {
				continue
			}
			res = append(res, item)
		}
		return res, nil
	}
}

func getFieldsInfoByTpl(ctx *gin.Context, tplId, serviceId int64) ([]*models.ArkTemplateServiceField, map[string]struct{}, map[string]*models.ArkTemplateServiceField, error) {
	arkTemplateServiceFieldList, err := models.ArkTemplateServiceFieldDao.GetFieldsByTplAndService(ctx, tplId, serviceId, nil)
	if err != nil {
		return nil, nil, nil, err
	}

	baseTmplFieldsMap := make(map[string]struct{})
	for _, item := range arkTemplateServiceFieldList {
		baseTmplFieldsMap[item.FieldKey] = struct{}{}
	}

	// 目前模板 tab 删除后，之前相关的绑定信息不会删除，这里按模板查询的场景都加上有效的 serviceId 参数
	serviceIds, err := getServiceIdsByTplId(ctx, tplId)
	if err != nil {
		return nil, nil, nil, err
	}

	arkTemplateServiceFieldMap, err := models.ArkTemplateServiceFieldDao.GetFieldsByTpl(ctx, tplId, serviceIds, nil)
	if err != nil {
		return nil, nil, nil, err
	}

	return arkTemplateServiceFieldList, baseTmplFieldsMap, arkTemplateServiceFieldMap, nil
}

func getFieldsInfoByTplId(ctx *gin.Context, tplId, serviceId int64) (arkTemplateServiceFieldList []*models.ArkTemplateServiceField, err error) {
	arkTemplateServiceFieldList = make([]*models.ArkTemplateServiceField, 0)
	if serviceId == 0 {
		arkTemplateServiceFieldList, err = models.ArkTemplateServiceFieldDao.GetFieldListByTpl(ctx, tplId, nil)
		if err != nil {
			return nil, nil
		}
	}

	arkTemplateServiceFieldList, err = models.ArkTemplateServiceFieldDao.GetFieldsByTplAndService(ctx, tplId, serviceId, nil)
	if err != nil {
		return nil, nil
	}

	return arkTemplateServiceFieldList, nil
}

func getServiceIdsByTplId(ctx *gin.Context, tplId int64) ([]int64, error) {
	// 目前模板 tab 删除后，之前相关的绑定信息不会删除，这里按模板查询的场景都加上有效的 serviceId 参数
	service, err := models.ArkTemplateServiceRelationDao.GetByTplID(ctx, []int{int(tplId)})
	if err != nil {
		return nil, err
	}
	serviceIds := make([]int64, 0)
	for _, item := range service {
		serviceIds = append(serviceIds, item.ServiceId)
	}
	return serviceIds, nil
}

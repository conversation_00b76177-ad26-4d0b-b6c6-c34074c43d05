package ui

import (
	"bou.ke/monkey"
	"deskcrm/controllers/http/ui/input/inputStudent"
	"deskcrm/helpers"
	"fmt"
	commonArkGo "git.zuoyebang.cc/fwyybase/fwyylibs/api/arkgo"
	"git.zuoyebang.cc/pkg/golib/v2/env"
	"github.com/gin-gonic/gin"
	"github.com/stretchr/testify/assert"
	"net/http"
	"net/http/httptest"
	"path"
	"runtime"
	"testing"
)

func getSourcePath() string {
	_, filename, _, _ := runtime.Caller(1)
	return path.Dir(filename)
}

func init() {
	dir := getSourcePath()
	env.SetAppName("testing")
	env.SetRootPath(dir + "/../..")

	helpers.PreInit()
	helpers.InitMysql()
	helpers.InitValidator()
	helpers.InitApiClient()
	helpers.InitRedis()
}

func createCtx() *gin.Context {
	w := httptest.NewRecorder()
	ctx, _ := gin.CreateTestContext(w)
	req, _ := http.NewRequest("GET", "/", nil)
	req.AddCookie(&http.Cookie{
		Name:  "ZYBIPSCAS",
		Value: "IPS_e13a2d2419c79f6819022eeca4f6a2f01754639315",
	})
	ctx.Request = req
	return ctx
}

func TestStudentService_FormatTags(t *testing.T) {
	// 创建测试上下文
	gin.SetMode(gin.TestMode)
	ctx := createCtx()

	// 设置输入参数
	param := &inputStudent.StudentDetailV1Param{
		StudentUid:   10086,
		CourseId:     20210001,
		LeadsId:      30001,
		AssistantUid: 40025,
		PersonUid:    50078,
		Tags:         "优秀,认真,活跃",
		TagArr:       []string{"优秀", "认真", "活跃"},
	}

	mockRules := &commonArkGo.GetFieldRuleByKeysResp{
		"tag1": {},
		"tag2": {},
	}

	patch1 := monkey.Patch(commonArkGo.GetFieldRuleByKeys,
		func(ctx *gin.Context, param commonArkGo.GetFieldRuleByKeysParam) (*commonArkGo.GetFieldRuleByKeysResp, error) {
			return mockRules, nil
		})
	defer patch1.Unpatch()

	service := studentService{}
	resp, err := service.StudentDetailV1(ctx, param)

	fmt.Println(resp)

	assert.Nil(t, err)
	assert.NotEmpty(t, resp)
}

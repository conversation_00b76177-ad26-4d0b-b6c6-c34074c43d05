package studentTagDefine

import (
	"deskcrm/consts"
	"deskcrm/controllers/http/ui/output/outputStudent"
	"git.zuoyebang.cc/fwyybase/fwyylibs/fwyyutils"
)

// 用户类型标签详情
var DetailTagUserType = map[string]outputStudent.Tag{
	"N1": {
		Label: "N1",
		Color: consts.TagColorGreen,
		Cname: "TextTag",
		Hover: "首次报名班课",
	},
	"N2": {
		Label: "N2",
		Color: consts.TagColorGray,
		Cname: "TextTag",
		Hover: "首次报名学科且不是首次报名班课",
	},
	"O1": {
		Label: "O1",
		Color: consts.TagColorGray,
		Cname: "TextTag",
		Hover: "连续1次同学科续报班课",
	},
	"O2": {
		Label: "O2",
		Color: consts.TagColorGray,
		Cname: "TextTag",
		Hover: "连续2次同学科续报班课",
	},
	"O3": {
		Label: "O3",
		Color: consts.TagColorGray,
		Cname: "TextTag",
		Hover: "连续3次同学科续报班课",
	},
	"O4": {
		Label: "O4",
		Color: consts.TagColorGray,
		Cname: "TextTag",
		Hover: "连续4次及以上同学科续报班课",
	},
	"Z": {
		Label: "Z",
		Color: consts.TagColorGray,
		Cname: "TextTag",
		Hover: "历史召回用户",
	},
}

// 是否联报标签详情
var DetailTagIsBound = outputStudent.Tag{
	Label: "联",
	Color: consts.TagColorPurple,
	Cname: "TextTag",
	Hover: "", // hover根据长短学季获取
}

// 是否续报标签详情
var DetailTagIsLevelTwo = outputStudent.Tag{
	Label: "续",
	Color: consts.TagColorBlue,
	Cname: "TextTag",
	Hover: "续报下一学季同年级同学科的课程",
}

// 是否转班标签详情
var DetailTagIsTransferCourse = outputStudent.Tag{
	Label: "转",
	Color: consts.TagColorOrange,
	Cname: "TextTag",
	Hover: "", // hover动态获取
}

// 是否插班标签详情
var DetailTagIsTransferStudent = outputStudent.Tag{
	Label: "插",
	Color: consts.TagColorBlue,
	Cname: "TextTag",
	Hover: "此学员在当前课程的第一个章节下课之后报名",
}

// 是否保价标签详情
var DetailTagPricePro = outputStudent.Tag{
	Label: "保",
	Color: consts.TagColorOrange,
	Cname: "TextTag",
	Hover: "当前学员享受保价特权",
}

// 单报留存标签详情
var DetailTagSingleApplyRetain = outputStudent.Tag{
	Label: "单报留存",
	Color: consts.TagColorGray,
	Cname: "TextTag",
	Hover: "学员单独购买当期课程（即购买当前课程时不是联报）同时又单独报名了春季课程",
}

// B类插班生标签详情
var DetailTagIsTransferStudentB = outputStudent.Tag{
	Label: "插|B类",
	Color: consts.TagColorGray,
	Cname: "TextTag",
	Hover: "学员在第三次核心课程下课后报名",
}

// 大促延期标签详情
var DetailTagDelaySale = outputStudent.Tag{
	Label: "大促延期",
	Color: consts.TagColorGray,
	Cname: "TextTag",
	Hover: "学员在大促期结束后可继续享受大促优惠",
}

// 延期插班标签详情
var DetailTagIsTransferStudentDelayed = outputStudent.Tag{
	Label: "延期插班",
	Color: consts.TagColorGray,
	Cname: "TextTag",
	Hover: "学员在2020年10月23日0点后报名",
}

// 微信添加状态标签详情
var DetailTagGuardianWechatLight = outputStudent.Tag{
	Label: "",
	Color: "",
	Cname: "Wechat",
	Hover: "",
	Prop:  "guardianWechatLight",
}

// 服务号关注状态标签详情
var DetailTagServiceType = outputStudent.Tag{
	Label: "",
	Color: "",
	Cname: "Follow",
	Hover: "",
	Prop:  "serviceType",
}

// Lpc学员类型标签详情
var DetailTagLpcUserType = map[string]outputStudent.Tag{
	consts.TypePreNew: {
		Label: "新",
		Color: consts.TagColorGreen,
		Cname: "TextTag",
		Hover: "",
	},
	consts.TypePrivateLong: {
		Label: "班",
		Color: consts.TagColorOrange,
		Cname: "TextTag",
		Hover: "",
	},
	consts.TypeOther: {
		Label: "其他",
		Color: consts.TagColorGray,
		Cname: "TextTag",
		Hover: "",
	},
}

// Lpc学员类型BL标签详情
var DetailTagBlLpcUserType = map[string]outputStudent.Tag{
	consts.TypePreNew: {
		Label:       "新",
		Color:       "rgba(42,189,144,1)",
		BorderColor: "rgba(42,189,144,0.3)",
		Cname:       "TextTag",
		Hover:       "",
	},
	consts.TypePrivateLong: {
		Label:       "班",
		Color:       "rgba(42,189,144,1)",
		BorderColor: "rgba(42,189,144,0.3)",
		Cname:       "TextTag",
		Hover:       "",
	},
	consts.TypeOther: {
		Label:       "其他",
		Color:       "rgba(42,189,144,1)",
		BorderColor: "rgba(42,189,144,0.3)",
		Cname:       "TextTag",
		Hover:       "",
	},
}

// Lpc渠道标签详情
var DetailTagChannel = outputStudent.Tag{
	Label: "端内购课",
	Color: consts.TagColorGray,
	Cname: "TextTag",
	Hover: "",
}

// Lpc数量标签详情
var DetailTagLpcNum = outputStudent.Tag{
	Label: "LPCx2",
	Color: consts.TagColorBlue,
	Cname: "TextTag",
	Hover: "",
}

// Lpc例子调配标签详情
var DetailTagLpcLeadsDeploy = outputStudent.Tag{
	Label: "调配",
	Color: consts.TagColorBlue,
	Cname: "TextTag",
	Hover: "",
}

// Lpc二级渠道标签详情
var DetailTagLpcSecondChannel = outputStudent.Tag{
	Label: "二级渠道",
	Color: consts.TagColorGray,
	Cname: "ChannelType",
	Hover: "",
}

// Lpc城市级别标签详情
var DetailTagLpcCityLevel = outputStudent.Tag{
	Label: "一线",
	Color: consts.TagColorGray,
	Cname: "TextTag",
	Hover: "",
}

// Lpc转化状态标签详情
var DetailTagLpcTranseStatus = outputStudent.Tag{
	Label: "转化",
	Color: consts.TagColorGray,
	Cname: "TextTag",
	Hover: "",
}

// Lpc用户分层标签详情
var DetailTagLpcUserDelamination = outputStudent.Tag{
	Label: "1",
	Color: consts.TagColorGray,
	Cname: "TextTag",
	Hover: "",
}

// 辅导用户分层标签详情
var DetailTagUserDelamination = outputStudent.Tag{
	Label: "A",
	Color: consts.TagColorGray,
	Cname: "TextTag",
	Hover: "",
}

// Lpc购课渠道标签详情
var DetailTagLpcChannel = outputStudent.Tag{
	Label: "端外",
	Color: consts.TagColorGray,
	Cname: "TextTag",
	Hover: "",
}

// 默认 tag 样式
var DetailTagDefault = outputStudent.Tag{
	Label: "",
	Color: consts.TagColorBlue,
	Cname: "TextTag",
	Hover: "",
}

// 直接展示原始值
var ShowValueInOriginal = map[string]struct{}{
	"deerLevelStr": {},
}

// 历史 tag key 和方舟字段映射
var TagKeyFieldMap = map[string]string{
	consts.TagUserType:            "userTypeV2",
	consts.TagLpcUserType:         "lpcTagInfo",
	consts.TagIsBound:             "studentInfoTagV3",
	consts.TagSingleApplyRetain:   "studentInfoTagV3",
	consts.TagIsLevelTwo:          "studentInfoTagV3",
	consts.TagIsTransferStudent:   "studentInfoTagV3",
	consts.TagIsTransferStudentB:  "studentInfoTagV3",
	consts.TagGuardianWechatLight: "studentInfoTagV3",
	consts.TagServiceType:         "serviceTypeStatus",
	consts.TagLpcNum:              "lpcTagInfo",
	consts.TagLpcLeadsDeploy:      "lpcTagInfo",
	consts.TagLpcSecondChannel:    "systemIntention_lastfrom",
	consts.TagLpcCityLevel:        "systemIntentionCityInfo",
	consts.TagLpcUserDelamination: "studentLayerTag",
}

func ReplaceTagArkKey(tags []string) []string {
	if len(tags) == 0 {
		return tags
	}

	result := make([]string, len(tags))
	for i, tag := range tags {
		// 检查标签是否在映射表中
		if field, exists := TagKeyFieldMap[tag]; exists {
			// 如果存在映射关系，则替换为对应的方舟字段名
			result[i] = field
		} else {
			// 如果不存在映射关系，则保持原样
			result[i] = tag
		}
	}

	return fwyyutils.ArrayUniqString(result)
}

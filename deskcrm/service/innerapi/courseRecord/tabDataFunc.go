package courseRecord

import (
	"deskcrm/consts"
	"deskcrm/controllers/http/ui/input/inputStudent"
	"deskcrm/controllers/http/ui/output/outputStudent"
	"git.zuoyebang.cc/fwyybase/fwyylibs/api/tower"
	"github.com/gin-gonic/gin"
)

const (
	TAB_CORE_DATA = consts.TAB_CORE_DATA
	TAB_KTBX      = consts.TAB_KTBX
	TAB_RJYL      = consts.TAB_RJYL
	TAB_MRYL      = consts.TAB_MRYL
	TAB_EXAM      = consts.TAB_EXAM
)

type TabProcessor struct {
	BaseData       *DataCourseRecordDepend
	DataQueryPoint *SingletonCourseRecord
	Param          *inputStudent.CourseRecordV2Param
}

func NewTabProcessor(data *DataCourseRecordDepend, dataQueryPoint *SingletonCourseRecord, param *inputStudent.CourseRecordV2Param) *TabProcessor {
	return &TabProcessor{
		BaseData:       data,
		DataQueryPoint: dataQueryPoint,
		Param:          param,
	}
}

func (tp *TabProcessor) ProcessTab(ctx *gin.Context, key string, item *outputStudent.CourseInfo) {
	var mapTabs2Func = map[string]func(ctx *gin.Context, item *outputStudent.CourseInfo){
		TAB_CORE_DATA: tp.SetTabListCoreData,
		TAB_KTBX:      tp.SetTabListKtbx,
		TAB_RJYL:      tp.SetTabListRjyl,
		TAB_MRYL:      tp.SetTabListMryl,
		TAB_EXAM:      tp.SetTabListExam,
	}

	if handler, ok := mapTabs2Func[key]; ok {
		handler(ctx, item)
	}
}

func (tp *TabProcessor) SetTab(item *outputStudent.CourseInfo, tabType string, isExport int) {
	item.TabList = append(item.TabList, outputStudent.TabInfo{
		Type:     tabType,
		Label:    consts.MAP_TAB_NAME[tabType],
		IsExport: isExport,
	})
}

func (tp *TabProcessor) SetTabListCoreData(ctx *gin.Context, item *outputStudent.CourseInfo) {
	if _, exists := tp.BaseData.TowerCourseInfo[item.CourseId]; !exists {
		return
	}
	tp.SetTab(item, TAB_CORE_DATA, 1)
}

func (tp *TabProcessor) SetTabListKtbx(ctx *gin.Context, item *outputStudent.CourseInfo) {
	if _, exists := tp.BaseData.TowerCourseInfo[item.CourseId]; !exists {
		return
	}
	tp.SetTab(item, TAB_KTBX, 0)
}

func (tp *TabProcessor) isLpc(ctx *gin.Context, item *outputStudent.CourseInfo) bool {
	// 前置条件
	if _, exists := tp.BaseData.CourseInfoMap[item.CourseId]; !exists {
		return false
	}
	queryData, err := tp.DataQueryPoint.GetInstanceData(ctx, "GetPriceTagInfo", []interface{}{item.CoursePriceTag})
	if err != nil {
		return false
	}
	serviceType := queryData.(int)
	return serviceType == 1
}

func (tp *TabProcessor) SetTabListRjyl(ctx *gin.Context, item *outputStudent.CourseInfo) {
	if tp.isLpc(ctx, item) {
		return
	}

	queryData2, err := tp.DataQueryPoint.GetInstanceData(ctx, "GetPriceTagListMap", []interface{}{})
	if err != nil {
		return
	}
	coursePricetagMap := queryData2.(map[int]tower.CourseTagInfo)
	// 小鹿扩科不展示
	if coursePricetagMap[item.CoursePriceTag].SaleMode == consts.SALE_MODE_FD && coursePricetagMap[item.CoursePriceTag].BizLine == consts.BIZ_LINE_DEER {
		return
	}
	tp.SetTab(item, TAB_RJYL, 0)
}

func (tp *TabProcessor) SetTabListMryl(ctx *gin.Context, item *outputStudent.CourseInfo) {
	if tp.isLpc(ctx, item) {
		return
	}

	queryData2, err := tp.DataQueryPoint.GetInstanceData(ctx, "GetPriceTagListMap", []interface{}{})
	if err != nil {
		return
	}
	coursePricetagMap := queryData2.(map[int]tower.CourseTagInfo)
	// 小鹿扩科不展示
	if coursePricetagMap[item.CoursePriceTag].SaleMode == consts.SALE_MODE_FD && coursePricetagMap[item.CoursePriceTag].BizLine == consts.BIZ_LINE_DEER {
		return
	}
	tp.SetTab(item, TAB_MRYL, 0)
}

func (tp *TabProcessor) SetTabListExam(ctx *gin.Context, item *outputStudent.CourseInfo) {
	if _, exists := tp.BaseData.CourseInfoMap[item.CourseId]; !exists {
		return
	}
	tp.SetTab(item, TAB_EXAM, 0)
}

package courseRecord

import (
	"deskcrm/api/coursetransgo"
	"deskcrm/components"
	"deskcrm/consts"
	"deskcrm/controllers/http/ui/input/inputStudent"
	"deskcrm/controllers/http/ui/output/outputStudent"
	commonArkGo "git.zuoyebang.cc/fwyybase/fwyylibs/api/arkgo"
	"github.com/gin-gonic/gin"
	"github.com/spf13/cast"
)

// 标签类型常量
const (
	TAG_ISLEVELTWO       = "isLevelTwo"       // 二级续报
	TAG_ISBOUND          = "isBound"          // 是否联报
	TAG_RESERVESTATUS    = "reserveStatus"    // 预约
	TAG_HASLATEENROLLEE  = "hasLateEnrollee"  // 插班生
	TAG_TAGREFUND        = "tagRefund"        // 购课标签
	TAG_ISL2RSPACESEASON = "isL2rSpaceSeason" // 隔季二级续报
)

var tagSet = map[string]string{
	TAG_ISLEVELTWO:       "studentInfoTagV3",     //isLevelTwo
	TAG_ISBOUND:          "studentInfoTagV3",     //isBound
	TAG_RESERVESTATUS:    "reserveStatus",        //reserveStatus
	TAG_HASLATEENROLLEE:  "studentInfoTagV3",     //isTransferStudent
	TAG_TAGREFUND:        "",                     // todo 新增字段 refund+changeType，绩效转化暂无批量接口，先通过 api 查询
	TAG_ISL2RSPACESEASON: "l2rSpaceSeasonStatus", //l2rSpaceSeasonStatus
}

// 颜色常量
const (
	COLOR_GREEN  = "green"
	COLOR_RED    = "red"
	COLOR_YELLOW = "yellow"
	COLOR_BLUE   = "blue"
	COLOR_GRAY   = "gray"
)

func GetTagSetArkKeys(keys []string) []string {
	values := make([]string, 0)
	for _, key := range keys {
		if val, exists := tagSet[key]; exists && val != "" {
			values = append(values, val)
		} else {
			values = append(values, key)
		}
	}
	return values
}

// ShoulderTagProcessor 处理肩标的结构体
type ShoulderTagProcessor struct {
	BaseData       *DataCourseRecordDepend
	DataQueryPoint *SingletonCourseRecord
	Param          *inputStudent.CourseRecordV2Param
}

// NewShoulderTagProcessor 创建肩标处理器
func NewShoulderTagProcessor(data *DataCourseRecordDepend, dataQueryPoint *SingletonCourseRecord, param *inputStudent.CourseRecordV2Param) *ShoulderTagProcessor {
	return &ShoulderTagProcessor{
		BaseData:       data,
		DataQueryPoint: dataQueryPoint,
		Param:          param,
	}
}

func GetTagGroup(keys []string) ([]string, []string) {
	diyTags := make([]string, 0)
	arkTags := make([]string, 0)

	for _, key := range keys {
		if _, ok := tagSet[key]; ok {
			diyTags = append(diyTags, key)
			continue
		}
		arkTags = append(arkTags, key)
	}
	return diyTags, arkTags
}

func (stp *ShoulderTagProcessor) ProcessTag(ctx *gin.Context, keys []string, param *inputStudent.CourseRecordV2Param, rulesTemp commonArkGo.GetFieldRuleByAppResp, item *outputStudent.CourseInfo) {
	// 方舟标签
	arkKeys := GetTagSetArkKeys(keys)
	formatData, err := commonArkGo.ArkFormat(ctx, commonArkGo.ArkFormatReq{
		AssistantUid:         param.AssistantUid,
		PersonUid:            param.PersonUid,
		CourseId:             param.CourseId,
		StudentUids:          []int64{param.StudentUid},
		LeadsIdMapStudentUid: map[int64]int64{param.LeadsId: param.StudentUid},
		FormatKeys:           components.Array.StringArrUnique(arkKeys), // 加上写死的一些 key
	})
	if err != nil {
		return
	}
	arkData := formatData.StudentList[param.StudentUid]

	for _, tagKey := range keys {
		// 处理 diy 样式 tag
		if fun, ok := stp.GetShoulderTagsToFuncMap()[tagKey]; ok {
			fun(ctx, item, arkData)
			continue
		}

		// 处理通用 tag
		if _, ok := arkData[tagKey]; !ok {
			continue
		}
		// 无映射，不展示
		if _, inFilterMap := rulesTemp[tagKey].FilterMap[cast.ToString(arkData[tagKey])]; !inFilterMap {
			continue
		}
		stp.SetTag(item, rulesTemp[tagKey].FilterMap[cast.ToString(arkData[tagKey])], COLOR_GRAY)
	}
}

// GetShoulderTagsToFuncMap 返回肩标签到处理函数的映射关系
func (stp *ShoulderTagProcessor) GetShoulderTagsToFuncMap() map[string]func(ctx *gin.Context, item *outputStudent.CourseInfo, arkData map[string]interface{}) {
	return map[string]func(ctx *gin.Context, item *outputStudent.CourseInfo, arkData map[string]interface{}){
		TAG_ISLEVELTWO:       stp.SetTagIsLevelTwo,
		TAG_ISBOUND:          stp.SetTagIsBound,
		TAG_RESERVESTATUS:    stp.SetTagReserveStatus,
		TAG_HASLATEENROLLEE:  stp.SetTagHasLateEnrollee,
		TAG_TAGREFUND:        stp.SetTagRefund,
		TAG_ISL2RSPACESEASON: stp.SetSpaceSeasonL2r,
	}
}

// SetTag 设置肩标通用方法
func (stp *ShoulderTagProcessor) SetTag(item *outputStudent.CourseInfo, label string, color string) {
	item.TagList = append(item.TagList, outputStudent.CourseRecordTag{
		Label: label,
		Color: color,
	})
}

func (stp *ShoulderTagProcessor) isLpc(ctx *gin.Context, item *outputStudent.CourseInfo) bool {
	// 前置条件
	if _, exists := stp.BaseData.CourseInfoMap[item.CourseId]; !exists {
		return false
	}
	queryData, err := stp.DataQueryPoint.GetInstanceData(ctx, "GetPriceTagInfo", []interface{}{item.CoursePriceTag})
	if err != nil {
		return false
	}
	serviceType := queryData.(int)
	return serviceType == 1
}

// SetTagIsLevelTwo 设置二级续报肩标
func (stp *ShoulderTagProcessor) SetTagIsLevelTwo(ctx *gin.Context, item *outputStudent.CourseInfo, arkData map[string]interface{}) {
	if stp.isLpc(ctx, item) {
		return
	}

	if d, exists := arkData["isLevelTwo"]; exists && cast.ToInt64(d) == 1 {
		stp.SetTag(item, "已续报", COLOR_GREEN)
	}
	return
}

// SetTagIsBound 设置是否联报肩标
func (stp *ShoulderTagProcessor) SetTagIsBound(ctx *gin.Context, item *outputStudent.CourseInfo, arkData map[string]interface{}) {
	if stp.isLpc(ctx, item) {
		return
	}

	if d, exists := arkData["isBound"]; exists {
		if cast.ToInt64(d) == 1 {
			stp.SetTag(item, "已联报", COLOR_GREEN)
		} else {
			stp.SetTag(item, "未联报", COLOR_GRAY)
		}
	}
}

// SetTagReserveStatus 设置预约状态肩标
func (stp *ShoulderTagProcessor) SetTagReserveStatus(ctx *gin.Context, item *outputStudent.CourseInfo, arkData map[string]interface{}) {
	if stp.isLpc(ctx, item) {
		return
	}

	if item.GradeStage == 1 {
		if d, exists := arkData["reserveStatus"]; exists && cast.ToInt64(d) == 1 {
			stp.SetTag(item, "已预约", COLOR_GREEN)
		}
	}
	return
}

// SetTagHasLateEnrollee 设置是否有插班生肩标
func (stp *ShoulderTagProcessor) SetTagHasLateEnrollee(ctx *gin.Context, item *outputStudent.CourseInfo, arkData map[string]interface{}) {
	if stp.isLpc(ctx, item) {
		return
	}

	if d, exists := arkData["isTransferStudent"]; exists && cast.ToInt64(d) == 1 {
		stp.SetTag(item, "插班生", COLOR_BLUE)
	}
	return
}

// SetTagRefund 设置退款肩标
func (stp *ShoulderTagProcessor) SetTagRefund(ctx *gin.Context, item *outputStudent.CourseInfo, arkData map[string]interface{}) {
	if stp.isLpc(ctx, item) {
		queryData, err := stp.DataQueryPoint.GetInstanceData(ctx, "GetCourseTransListByStaffStudent", []interface{}{stp.Param.PersonUid, stp.Param.StudentUid, stp.BaseData.CourseIDSet})
		if err != nil {
			return
		}
		courseTransData := queryData.(map[int64]*coursetransgo.ScTransData)
		if data, exists2 := courseTransData[item.CourseId]; !exists2 {
			if data.Refund > 0 {
				stp.SetTag(item, "已退课", COLOR_RED)
			}
		} else {
			if data.ChangeType == 1 {
				stp.SetTag(item, "调出", COLOR_BLUE)
			} else {
				stp.SetTag(item, "调入", COLOR_BLUE)

			}
		}
		return
	}

	// 辅导
	if item.TradeStatus == consts.TradeStatusRefunded || item.TradeStatus == consts.TradeStatusRefundPart {
		stp.SetTag(item, "已退课", COLOR_RED)
	} else {
		stp.SetTag(item, "购课", COLOR_BLUE)
	}
	return
}

// SetSpaceSeasonL2r 设置隔季二级续报肩标
func (stp *ShoulderTagProcessor) SetSpaceSeasonL2r(ctx *gin.Context, item *outputStudent.CourseInfo, arkData map[string]interface{}) {
	if stp.isLpc(ctx, item) {
		return
	}

	if d, exists := arkData["l2rSpaceSeasonStatus"]; exists {
		if cast.ToInt64(d) == 1 {
			stp.SetTag(item, "已隔季二级续报", COLOR_GREEN)
		} else {
			stp.SetTag(item, "未隔季二级续报", COLOR_RED)
		}
	}
}

package courseRecord

import (
	"deskcrm/api/assistantdeskgo"
	"deskcrm/api/moat"
	"deskcrm/components"
	"deskcrm/consts"
	"deskcrm/controllers/http/ui/input/inputStudent"
	"deskcrm/controllers/http/ui/output/outputStudent"
	"deskcrm/util"
	"fmt"
	"git.zuoyebang.cc/pkg/golib/v2/zlog"
	"github.com/gin-gonic/gin"
)

const (
	SHOULDER_STAGETEST              = "stageTest"       // 阶段测试
	SHOULDER_STAGEREPORT            = "stageReport"     // 阶段报告
	SHOULDER_STAGEREPORTLPC         = "stageReportLpc"  // 阶段报告
	SHOULDER_UNITTEST               = "unitTest"        // 单元测试
	SHOULDER_STUDYREPORT            = "studyReport"     // 学习报告
	SHOULDER_CHECKTEST              = "checkTest"       // 摸底测试
	SHOULDER_TOTALSCORE             = "totalScore"      // 总学分
	SHOULDER_SCOREEXCHANGE          = "scoreExchange"   // 学分兑换
	SHOULDER_BOOKSEND               = "bookSend"        // 物流信息
	SHOULDER_EXAMERRORBUTTON        = "examErrorButton" // 错题本
	SHOULDER_ERROR_TASK_BUTTON      = "errorTaskButton" // 错题任务
	SHOULDER_LEARN_UNSCRAMBLE       = "learnUnscramble"
	SHOULDER_LEARN_UNSCRAMBLE_V2    = "learnUnscrambleV2"
	SHOULDER_SCOREENCOURAGE         = "scoreEncourage" // 学分激励
	SHOULDER_GGSK                   = "ggsk"           // 高光时刻
	SHOULDER_COURSE_REPORT          = "courseReport"
	SHOULDER_WEEK_REPORT            = "weekReport"
	SHOULDER_SEMESTER_REPORT_SENIOR = "semesterReportSenior" // 学期报告高中
	SHOULDER_GJK_PLAN               = "gjkPlan"              // 高价课学习计划控件
	SHOULDER_GJK_COMPLETE_RATE      = "gjkCompleteRate"      // 整体进度完成情况
)

type ShoulderProcessor struct {
	BaseData       *DataCourseRecordDepend
	DataQueryPoint *SingletonCourseRecord
	Param          *inputStudent.CourseRecordV2Param
}

func NewShoulderProcessor(data *DataCourseRecordDepend, dataQueryPoint *SingletonCourseRecord, param *inputStudent.CourseRecordV2Param) *ShoulderProcessor {
	return &ShoulderProcessor{
		DataQueryPoint: dataQueryPoint,
		BaseData:       data,
		Param:          param,
	}
}

func (sp *ShoulderProcessor) ProcessShoulders(ctx *gin.Context, key string, item *outputStudent.CourseInfo) {
	var mapShoulders2Func = map[string]func(ctx *gin.Context, item *outputStudent.CourseInfo){
		SHOULDER_STAGETEST:              sp.SetShoulderStageTest,
		SHOULDER_STAGEREPORT:            sp.SetShoulderStageReport,
		SHOULDER_STAGEREPORTLPC:         sp.SetShoulderStageReportLpc,
		SHOULDER_UNITTEST:               sp.SetShoulderUnitTest,
		SHOULDER_STUDYREPORT:            sp.SetShoulderStudyReport,
		SHOULDER_CHECKTEST:              sp.SetShoulderCheckTest,
		SHOULDER_TOTALSCORE:             sp.SetShoulderTotalScore,
		SHOULDER_SCOREEXCHANGE:          sp.SetShoulderScoreExchange,
		SHOULDER_BOOKSEND:               sp.SetShoulderBookSend,
		SHOULDER_EXAMERRORBUTTON:        sp.SetShoulderExamErrorButton,
		SHOULDER_ERROR_TASK_BUTTON:      sp.SetShoulderErrorTaskButton,
		SHOULDER_LEARN_UNSCRAMBLE:       sp.SetShoulderLearnUnscramble,
		SHOULDER_LEARN_UNSCRAMBLE_V2:    sp.SetShoulderLearnUnscrambleV2,
		SHOULDER_SCOREENCOURAGE:         sp.SetShoulderScoreEncourage,
		SHOULDER_GGSK:                   sp.SetShoulderCourseReviewUrl,
		SHOULDER_COURSE_REPORT:          sp.SetShoulderCourseReport,
		SHOULDER_WEEK_REPORT:            sp.SetShoulderWeekReport,
		SHOULDER_SEMESTER_REPORT_SENIOR: sp.SetShoulderSemesterReportSenior,
		SHOULDER_GJK_PLAN:               sp.SetShoulderGjkPlan,
		SHOULDER_GJK_COMPLETE_RATE:      sp.SetShoulderGjkCompleteRate,
	}

	if handler, ok := mapShoulders2Func[key]; ok {
		handler(ctx, item)
	}
}

func (sp *ShoulderProcessor) SetShoulderStageTest(ctx *gin.Context, item *outputStudent.CourseInfo) {
	if _, exists := sp.BaseData.TowerCourseInfo[item.CourseId]; !exists {
		return
	}

	item.Shoulder = append(item.Shoulder, SHOULDER_STAGETEST)
}

func (sp *ShoulderProcessor) SetShoulderStageReport(ctx *gin.Context, item *outputStudent.CourseInfo) {
	if _, exists := sp.BaseData.TowerCourseInfo[item.CourseId]; !exists {
		return
	}
	if sp.isLpc(ctx, item) {
		return
	}

	item.Shoulder = append(item.Shoulder, SHOULDER_STAGEREPORT)
}

func (sp *ShoulderProcessor) SetShoulderStageReportLpc(ctx *gin.Context, item *outputStudent.CourseInfo) {
	if _, exists := sp.BaseData.TowerCourseInfo[item.CourseId]; !exists {
		return
	}
	if !sp.isLpc(ctx, item) {
		return
	}
	item.Shoulder = append(item.Shoulder, SHOULDER_STAGEREPORTLPC)
}

func (sp *ShoulderProcessor) SetShoulderUnitTest(ctx *gin.Context, item *outputStudent.CourseInfo) {
	if _, exists := sp.BaseData.TowerCourseInfo[item.CourseId]; !exists {
		return
	}
	if sp.isLpc(ctx, item) {
		return
	}
	item.Shoulder = append(item.Shoulder, SHOULDER_UNITTEST)
}

func (sp *ShoulderProcessor) SetShoulderStudyReport(ctx *gin.Context, item *outputStudent.CourseInfo) {
	if _, exists := sp.BaseData.TowerCourseInfo[item.CourseId]; !exists {
		return
	}
	if sp.isLpc(ctx, item) {
		return
	}
	item.Shoulder = append(item.Shoulder, SHOULDER_STUDYREPORT)
}

func (sp *ShoulderProcessor) SetShoulderCheckTest(ctx *gin.Context, item *outputStudent.CourseInfo) {
	if _, exists := sp.BaseData.TowerCourseInfo[item.CourseId]; !exists {
		return
	}
	if sp.isLpc(ctx, item) {
		return
	}
	item.Shoulder = append(item.Shoulder, SHOULDER_CHECKTEST)
}

func (sp *ShoulderProcessor) SetShoulderTotalScore(ctx *gin.Context, item *outputStudent.CourseInfo) {
	if _, exists := sp.BaseData.TowerCourseInfo[item.CourseId]; !exists {
		return
	}
	if sp.isLpc(ctx, item) {
		return
	}

	item.Shoulder = append(item.Shoulder, SHOULDER_TOTALSCORE)
}

func (sp *ShoulderProcessor) SetShoulderScoreExchange(ctx *gin.Context, item *outputStudent.CourseInfo) {
	if _, exists := sp.BaseData.TowerCourseInfo[item.CourseId]; !exists {
		return
	}
	if sp.isLpc(ctx, item) {
		return
	}
	//小学和学前
	if components.Array.InArrayInt64(int64(item.GradeStage), []int64{1, 60}) {
		item.Shoulder = append(item.Shoulder, SHOULDER_SCOREEXCHANGE)
	}
}

func (sp *ShoulderProcessor) SetShoulderBookSend(ctx *gin.Context, item *outputStudent.CourseInfo) {
	if _, exists := sp.BaseData.TowerCourseInfo[item.CourseId]; !exists {
		return
	}
	if sp.isLpc(ctx, item) {
		return
	}
	item.Shoulder = append(item.Shoulder, SHOULDER_BOOKSEND)
}

func (sp *ShoulderProcessor) SetShoulderExamErrorButton(ctx *gin.Context, item *outputStudent.CourseInfo) {
	if _, exists := sp.BaseData.TowerCourseInfo[item.CourseId]; !exists {
		return
	}
	if sp.isLpc(ctx, item) {
		return
	}
	// 配置判断
	if !util.GrayConfig.ShowExamErrorBook(ctx, item.DeptId, item.Grade) {
		return
	}
	// 有无错题判断
	queryData, err := sp.DataQueryPoint.GetInstanceData(ctx, "GetAllHomeworkError", []interface{}{sp.BaseData.CourseIDSet, sp.Param.StudentUid})
	if err != nil {
		return
	}
	allHomeworkError := queryData.(map[int64][]int64)
	if len(allHomeworkError) == 0 {
		return
	}
	if components.Array.InArrayInt64(sp.Param.CourseId, allHomeworkError[sp.Param.StudentUid]) {
		item.Shoulder = append(item.Shoulder, SHOULDER_EXAMERRORBUTTON)
	}
}

func (sp *ShoulderProcessor) SetShoulderErrorTaskButton(ctx *gin.Context, item *outputStudent.CourseInfo) {
	if _, exists := sp.BaseData.TowerCourseInfo[item.CourseId]; !exists {
		return
	}
	if sp.isLpc(ctx, item) {
		return
	}
	// 配置判断
	if !util.GrayConfig.ShowExamErrorBook(ctx, item.DeptId, item.GradeStage) {
		return
	}

	item.Shoulder = append(item.Shoulder, SHOULDER_ERROR_TASK_BUTTON)
}

// SetShoulderLearnUnscramble jiangkang 确认已经可以下线
func (sp *ShoulderProcessor) SetShoulderLearnUnscramble(ctx *gin.Context, item *outputStudent.CourseInfo) {
	return
}

func (sp *ShoulderProcessor) SetShoulderLearnUnscrambleV2(ctx *gin.Context, item *outputStudent.CourseInfo) {
	if _, exists := sp.BaseData.TowerCourseInfo[item.CourseId]; !exists {
		return
	}
	if sp.isLpc(ctx, item) {
		return
	}

	hit, err := assistantdeskgo.NewClient().GrayHit(ctx, sp.Param.PersonUid, "assistantdesk_learn_unscramble_v2_gray_conf")
	if hit && err == nil {
		item.Shoulder = append(item.Shoulder, SHOULDER_LEARN_UNSCRAMBLE_V2)
	}
	if err != nil {
		zlog.Warnf(ctx, "SetShoulderLearnUnscrambleV2 Error[gray hit error] Detail[%v]", err)
	}
}

func (sp *ShoulderProcessor) SetShoulderScoreEncourage(ctx *gin.Context, item *outputStudent.CourseInfo) {
	if _, exists := sp.BaseData.TowerCourseInfo[item.CourseId]; !exists {
		return
	}
	if !sp.isLpc(ctx, item) {
		return
	}

	item.Shoulder = append(item.Shoulder, SHOULDER_SCOREENCOURAGE)
}

func (sp *ShoulderProcessor) SetShoulderCourseReviewUrl(ctx *gin.Context, item *outputStudent.CourseInfo) {
	if _, exists := sp.BaseData.TowerCourseInfo[item.CourseId]; !exists {
		return
	}
	if !sp.isLpc(ctx, item) {
		return
	}

	if item.CourseId == sp.Param.CourseId {
		url := getVisitShortUrlBySubjectId(ctx, item.CourseId, sp.Param.PersonUid, sp.Param.LeadsId, item.Subject, "teacher")
		item.CourseReviewUrl = url
		item.Shoulder = append(item.Shoulder, SHOULDER_GGSK)
	}
}

func (sp *ShoulderProcessor) SetShoulderCourseReport(ctx *gin.Context, item *outputStudent.CourseInfo) {
	item.Shoulder = append(item.Shoulder, SHOULDER_COURSE_REPORT)
}

func (sp *ShoulderProcessor) SetShoulderWeekReport(ctx *gin.Context, item *outputStudent.CourseInfo) {
	item.Shoulder = append(item.Shoulder, SHOULDER_WEEK_REPORT)
}

func (sp *ShoulderProcessor) SetShoulderSemesterReportSenior(ctx *gin.Context, item *outputStudent.CourseInfo) {
	if item.GradeStage == 30 { //高中
		item.Shoulder = append(item.Shoulder, SHOULDER_SEMESTER_REPORT_SENIOR)
	}
}

func (sp *ShoulderProcessor) SetShoulderGjkPlan(ctx *gin.Context, item *outputStudent.CourseInfo) {
	if d, ok := sp.BaseData.LearnPlanStatus[item.CourseId]; ok && d.PlanExists {
		item.Shoulder = append(item.Shoulder, SHOULDER_GJK_PLAN)
	}
}

func (sp *ShoulderProcessor) SetShoulderGjkCompleteRate(ctx *gin.Context, item *outputStudent.CourseInfo) {
	if d, ok := sp.BaseData.LearnPlanStatus[item.CourseId]; ok && d.PlanExists {
		item.Shoulder = append(item.Shoulder, SHOULDER_GJK_COMPLETE_RATE)
	}
}

func (sp *ShoulderProcessor) isLpc(ctx *gin.Context, item *outputStudent.CourseInfo) bool {
	// 前置条件
	if _, exists := sp.BaseData.CourseInfoMap[item.CourseId]; !exists {
		return false
	}
	queryData, err := sp.DataQueryPoint.GetInstanceData(ctx, "GetPriceTagInfo", []interface{}{item.CoursePriceTag})
	if err != nil {
		return false
	}
	serviceType := queryData.(int)
	return serviceType == 1
}

// getVisitShortUrlBySubjectId 根据科目ID获取访问短链接
func getVisitShortUrlBySubjectId(ctx *gin.Context, courseId, lpcUid, leadsId int64, subjectId int, to string) string {
	if lpcUid == 0 || courseId == 0 || leadsId == 0 || subjectId == 0 {
		zlog.Warnf(ctx, "Error[check parms error] Detail[lpcUid:%d][courseId:%d][leadsId:%d][subjectId:%d]",
			lpcUid, courseId, leadsId, subjectId)
		return ""
	}

	if to == "" {
		to = "user"
	}

	var url string
	if subjectId == consts.SubjectMath {
		url = fmt.Sprintf("%s?highlightKey=%s_%s_%s&__to=%s",
			consts.URL_H5_PATH,
			util.EncodeUid(ctx, courseId),
			util.EncodeUid(ctx, lpcUid),
			util.EncodeUid(ctx, leadsId),
			to)
	} else if subjectId == consts.SubjectEnglish {
		url = fmt.Sprintf("%s?highlightKey=%s_%s_%s&__to=%s",
			consts.ENGLISH_URL_H5_PATH,
			util.EncodeUid(ctx, courseId),
			util.EncodeUid(ctx, lpcUid),
			util.EncodeUid(ctx, leadsId),
			to)
	} else {
		return ""
	}

	shortUrl, err := moat.NewClient().GetShortUrl(ctx, url)
	if err != nil {
		zlog.Warnf(ctx, "Error[get short url error] Detail[%s]", err.Error())
		return url
	}
	return shortUrl
}

package courseRecord

import (
	"deskcrm/api/coursetransgo"
	"deskcrm/api/dal"
	"deskcrm/api/das"
	"deskcrm/components"
	"deskcrm/consts"
	"deskcrm/service/arkBase/dataQuery"
	"deskcrm/util"
	"errors"
	"reflect"
	"sync"

	"git.zuoyebang.cc/fwyybase/fwyylibs/api/tower"
	"git.zuoyebang.cc/pkg/golib/v2/utils"
	"git.zuoyebang.cc/pkg/golib/v2/zlog"
	"github.com/gin-gonic/gin"
	jsoniter "github.com/json-iterator/go"
	"github.com/spf13/cast"
	"golang.org/x/sync/singleflight"
)

var luFields = []string{
	"courseId",
	"studentUid",
	"lessonId",
	"tradeStatus",
	"isHomeworkSubmit",
	"homeworkSubmissions",
	"homeworkPracticeParticipateNum",
	"homeworkPracticeCorrectNum",
	"homeworkPracticeTotalNum", // 巩固练习
	"synchronousPracticeParticipateNum",
	"synchronousPracticeCorrectNum",
	"synchronousPracticeTotalNum", // 同步练习
	"stageTestExamParticipateNum",
	"stageTestExamParticipateNum",
	"stageTestExamTotalNum", // 阶段测
	"tangTangExamParticipateNum",
	"tangTangExamCorrectNum",
	"tangTangExamTotalNum", // 堂堂测
	"previewParticipateNum",
	"previewCorrectNum",
	"previewTotalNum", // 预习
}

const FuncOutputNum = 2

type SingletonCourseRecord struct {
	InstanceData map[string]interface{}
	BaseData     *DataCourseRecordDepend
	sg           *singleflight.Group
	lock         *sync.Mutex
}

func NewQuery(baseData *DataCourseRecordDepend) *SingletonCourseRecord {
	c := &SingletonCourseRecord{
		BaseData:     baseData,
		InstanceData: map[string]interface{}{},
		sg:           &singleflight.Group{},
		lock:         &sync.Mutex{},
	}
	return c
}

func (s *SingletonCourseRecord) getSign(ctx *gin.Context, v interface{}) (sign string, err error) {
	jsonData, err := jsoniter.MarshalToString(v)
	if err != nil {
		return
	}
	return utils.Md5(string(jsonData)), nil
}

func (s *SingletonCourseRecord) getCacheData(ctx *gin.Context, signData interface{}) (isCache bool, output interface{}) {
	sign, err := s.getSign(ctx, signData)
	if err != nil {
		return false, nil
	}
	s.lock.Lock()
	cacheData, ok := s.InstanceData[sign]
	s.lock.Unlock()

	if !ok {
		return false, nil
	}

	return true, cacheData
}

func (s *SingletonCourseRecord) setCacheData(ctx *gin.Context, signData interface{}, data interface{}) (isCache bool) {
	sign, err := s.getSign(ctx, signData)
	if err != nil {
		return false
	}

	s.lock.Lock()
	s.InstanceData[sign] = data
	s.lock.Unlock()
	return true
}

func (s *SingletonCourseRecord) GetInstanceData(ctx *gin.Context, dataQueryfunc string, queryParams []interface{}) (resp interface{}, err error) {
	signData := []interface{}{dataQueryfunc, queryParams}
	sign, _ := s.getSign(ctx, signData)

	queryData, err, _ := s.sg.Do(sign, func() (interface{}, error) {
		defer func() {
			if r := recover(); r != nil {
				zlog.Errorf(ctx, "GetInstanceData panic err : %+v", r)
				components.Util.PanicTrace(ctx)
			}
		}()

		if ok, data := s.getCacheData(ctx, signData); ok {
			return data, nil
		}

		refPoint := reflect.ValueOf(s).MethodByName(dataQueryfunc)
		if refPoint.Kind() != reflect.Func {
			zlog.AddNotice(ctx, "fotmat router ark func not found", dataQueryfunc)
		}
		paramNum := refPoint.Type().NumIn()
		respNum := refPoint.Type().NumOut()

		if paramNum != len(queryParams)+1 {
			return nil, errors.New("函数参数数量不匹配，无法调用")
		}

		if respNum != FuncOutputNum {
			return nil, errors.New("函数返回值只能有两个且最后一个必须为error")
		}

		outs := make([]reflect.Type, 0, respNum)
		for i := 0; i < respNum; i++ {
			arg := refPoint.Type().Out(i)
			outs = append(outs, arg)
		}

		//检查返回的最后一个参数是否是error
		if !outs[len(outs)-1].AssignableTo(reflect.TypeOf((*error)(nil)).Elem()) {
			return nil, errors.New("最后一个参数必须是error")
		}

		callParam := []reflect.Value{
			reflect.ValueOf(ctx),
		}
		for _, param := range queryParams {
			callParam = append(callParam, reflect.ValueOf(param))
		}
		resultValues := refPoint.Call(callParam)
		if len(resultValues) != FuncOutputNum {
			return nil, errors.New("ark query dynamic call error")
		}

		end := len(resultValues) - 1
		if resultValues[end].Interface() != nil {
			return nil, resultValues[end].Interface().(error)
		}

		_ = s.setCacheData(ctx, signData, resultValues[0].Interface())
		return resultValues[0].Interface(), nil
	})

	return queryData, err
}

func (s *SingletonCourseRecord) GetPriceTagInfo(ctx *gin.Context, coursePriceTag int) (int, error) {
	info, err := tower.GetPriceTagInfo(ctx, tower.GetPriceTagInfoReq{Id: int64(coursePriceTag)})
	if err != nil {
		return 0, err
	}
	return info.Info.ServiceType, nil
}

func (s *SingletonCourseRecord) GetPriceTagListMap(ctx *gin.Context) (map[int]tower.CourseTagInfo, error) {
	info, err := tower.GetPriceTagListMap(ctx)
	if err != nil {
		return info, err
	}
	return info, nil
}

func (s *SingletonCourseRecord) GetCourseTransListByStaffStudent(ctx *gin.Context, personUid int64, studentUid int64, courseIds []int64) (resp map[int64]*coursetransgo.ScTransData, err error) {
	resp = make(map[int64]*coursetransgo.ScTransData)
	scTransDataList, err := coursetransgo.NewClient().GetCourseTransListByStaffStudent(ctx, personUid, studentUid, courseIds)
	if err != nil {
		return
	}

	result := make(map[int64]*coursetransgo.ScTransData)
	for _, item := range scTransDataList {
		result[item.CourseId] = &item
	}
	return
}

func (s *SingletonCourseRecord) GetAllHomeworkError(ctx *gin.Context, courseIds []int64, studentUid int64) (hasErrMap map[int64][]int64, err error) {
	hasErrorStudentUids := make(map[int64][]int64)
	// get lu info
	studentLessonData, err := dataQuery.New().GetLuDataByCourseIds(ctx, courseIds, studentUid, luFields)
	if err != nil {
		return
	}
	components.DebugfWithJSON(ctx, "GetLuDataByCourseIds res,%s", studentLessonData)
	lessonIds := components.Array.UniqueInt64(util.MapKeys(studentLessonData))
	if len(lessonIds) == 0 {
		return
	}

	var dasFields = []string{
		"courseId",
		"lessonId",
		"studentUid",
		"homeworkStatus",
		"homeworkRecorrect",
	}
	dasAllLessonData, err := das.GetDasLessonsData(ctx, []int64{studentUid}, lessonIds, dasFields)
	if err != nil {
		return
	}
	components.DebugfWithJSON(ctx, "GetDasLessonsData res,%s", dasAllLessonData)

	// 判定课程是否属于订正巩固练习
	isCorrectHomework := make(map[int64]bool)
	for _, courseIndo := range s.BaseData.CourseInfoMap {
		isCorrectHomework[int64(courseIndo.CourseId)] = dal.IsHomeworkAmendCourse(ctx, courseIndo)
	}
	components.DebugfWithJSON(ctx, "IsHomeworkAmendCourseByDal res,%s", isCorrectHomework)

	// 计算学生有错题的课程
	for _, lessonData := range studentLessonData {
		courseId := lessonData.CourseId
		lessonId := lessonData.LessonId

		// 检查学生是否已有错误记录
		if errorCourses, exists := hasErrorStudentUids[studentUid]; exists {
			// 检查当前课程是否已在错误列表中
			isErrorExists := false
			for _, errCourseId := range errorCourses {
				if errCourseId == courseId {
					isErrorExists = true
					break
				}
			}
			if isErrorExists {
				continue
			}
		}

		// 同步练习 对答不一致存在错题
		if lessonData.SynchronousPracticeParticipateNum != lessonData.SynchronousPracticeCorrectNum {
			hasErrorStudentUids[studentUid] = append(hasErrorStudentUids[studentUid], courseId)
			continue
		}

		// 预习 对答不一致存在错题
		if lessonData.PreviewParticipateNum != lessonData.PreviewCorrectNum {
			hasErrorStudentUids[studentUid] = append(hasErrorStudentUids[studentUid], courseId)
			continue
		}

		// 堂堂测 对答不一致存在错题
		if lessonData.TangTangExamParticipateNum != lessonData.TangTangExamCorrectNum {
			hasErrorStudentUids[studentUid] = append(hasErrorStudentUids[studentUid], courseId)
			continue
		}

		// 阶段测 对答不一致存在错题
		if lessonData.StageTestExamParticipateNum != lessonData.StageTestExamCorrectNum {
			hasErrorStudentUids[studentUid] = append(hasErrorStudentUids[studentUid], courseId)
			continue
		}

		// 巩固练习
		homeworkSubmissions := cast.ToInt64(lessonData.HomeworkSubmissions)
		// 提交次数大于1 有错题
		if homeworkSubmissions > 1 {
			hasErrorStudentUids[studentUid] = append(hasErrorStudentUids[studentUid], courseId)
			continue
		} else if homeworkSubmissions == 1 {
			studentData, studentExists := dasAllLessonData[studentUid]
			if !studentExists {
				continue
			}
			lessonDataTemp, lessonExists := studentData[lessonId]
			if !lessonExists {
				continue
			}
			if isCorrectHomework[courseId] {
				// 是订正巩固练习
				homeworkRecorrect := lessonDataTemp.HomeworkRecorrect
				if homeworkRecorrect == consts.HOMEWORK_CORRECT_STATUS_TB_CORRECTED {
					continue
				}
			} else {
				// 普通的巩固练习
				homeworkStatus := lessonDataTemp.HomeworkStatus
				if homeworkStatus == consts.HOMEWORK_STATUS_SUBMIT {
					continue
				}
			}

			// 对答总不一致存在错题
			if lessonData.HomeworkPracticeCorrectNum != lessonData.HomeworkPracticeParticipateNum {
				hasErrorStudentUids[studentUid] = append(hasErrorStudentUids[studentUid], courseId)
				continue
			}
		}
	}
	return
}

package courseRecord

import (
	"deskcrm/api/dal"
	"deskcrm/components"
	"deskcrm/consts"
	"deskcrm/models"
	"deskcrm/stru/keepDetail"
	"deskcrm/util"
	baseTower "git.zuoyebang.cc/fwyybase/fwyylibs/api/tower"
	"git.zuoyebang.cc/pkg/golib/v2/zlog"
	"github.com/gin-gonic/gin"
	json "github.com/json-iterator/go"
)

type ConfigCourseRecordService struct {
}

func NewConfigServiceCourseRecord() *ConfigCourseRecordService {
	return &ConfigCourseRecordService{}
}

func (s *ConfigCourseRecordService) GetShouldersAndTagsAndTabsByCourseId(ctx *gin.Context, courseId int64) (resp keepDetail.CourseSchema, err error) {
	resp = keepDetail.CourseSchema{}
	courseInfoMap, err := dal.GetCourseBaseByCourseIds(ctx, []int64{courseId}, []string{})
	if err != nil {
		return
	}
	if len(courseInfoMap) == 0 {
		return
	}

	towerCourseInfo, err := baseTower.GetCourseInfo(ctx, courseId)
	if err != nil {
		return
	}

	res, err := s.GetShouldersAndTagsAndTabsByCourseIds(ctx, []dal.CourseInfo{courseInfoMap[courseId]}, map[int64]baseTower.GetCourseInfoRsp{courseId: *towerCourseInfo})
	if err != nil {
		return
	}
	resp = res[courseId]
	return
}

func (s *ConfigCourseRecordService) GetShouldersAndTagsAndTabsByCourseIds(ctx *gin.Context, courseList []dal.CourseInfo, towerCourseMap map[int64]baseTower.GetCourseInfoRsp) (resp keepDetail.CourseSchemaResponse, err error) {
	resp = keepDetail.CourseSchemaResponse{}
	if len(courseList) == 0 {
		return
	}

	// 1. 优先课程命中
	recordSchemaCourseList, err := models.CourseRecordSchemaDao.GetListByTypeAndStatus(ctx, consts.CourseRecordSchemaTypeCourse, consts.CourseRecordSchemaStatusIsOnline)
	if err != nil {
		return
	}

	// 处理按课程生效的配置
	findCourseIdMap := make(map[int64]struct{})
	if len(recordSchemaCourseList) > 0 {
		for _, courseInfo := range courseList {
			courseId := int64(courseInfo.CourseId)
			for _, record := range recordSchemaCourseList {
				// hit
				if util.ContainsInt64InString(record.CourseIds, courseId) {
					var schema keepDetail.CourseSchemaDaoStruct
					err = json.Unmarshal([]byte(record.Schema), &schema)
					if err != nil {
						zlog.Warnf(ctx, "Failed to parse schema for courseId %d: %v", courseId, err)
						continue
					}
					schema.CourseRecordSchema.SchemaID = int(record.SchemaId)
					resp[courseId] = schema.CourseRecordSchema
					findCourseIdMap[courseId] = struct{}{}
					break
				}
			}
		}
	}

	notFindCourseId := make([]int64, 0)
	notFindSource := make([]int64, 0)
	notFindCourseInfo := make([]dal.CourseInfo, 0)
	for _, courseInfo := range courseList {
		if _, found := findCourseIdMap[int64(courseInfo.CourseId)]; !found {
			notFindCourseId = append(notFindCourseId, int64(courseInfo.CourseId))
			notFindSource = append(notFindSource, courseInfo.Source)
			notFindCourseInfo = append(notFindCourseInfo, courseInfo)
		}
	}

	components.DebugfWithJSON(ctx, "GetShouldersAndTagsAndTabsByCourseId 1, result:%s", resp)

	if len(notFindCourseId) == 0 {
		return
	}

	components.DebugfWithJSON(ctx, "GetShouldersAndTagsAndTabsByCourseId 2, result:%s", notFindCourseId)

	// get by business id
	courseRecordSchemaList, err := models.CourseRecordSchemaDao.GetListByBusinessLineIdsAndTypeStatus(ctx, components.Array.UniqueInt64(notFindSource), consts.CourseRecordSchemaTypeRule, consts.CourseRecordSchemaStatusIsOnline)
	if err != nil {
		return
	}
	if len(courseRecordSchemaList) == 0 {
		return
	}

	schemaBusinessLineMap := make(map[int64][]models.CourseRecordSchema)
	for _, dbData := range courseRecordSchemaList {
		if _, ok := schemaBusinessLineMap[dbData.BusinessLineId]; !ok {
			schemaBusinessLineMap[dbData.BusinessLineId] = make([]models.CourseRecordSchema, 0)
		}
		schemaBusinessLineMap[dbData.BusinessLineId] = append(schemaBusinessLineMap[dbData.BusinessLineId], dbData)
	}

	for _, info := range notFindCourseInfo {
		if _, ok := schemaBusinessLineMap[int64(info.Source)]; !ok {
			continue
		}

		for _, schemaInfo := range schemaBusinessLineMap[int64(info.Source)] {
			if !util.ContainsInt64InString(schemaInfo.NewCourseTypes, int64(info.NewCourseType)) {
				continue
			}

			if !util.ContainsInt64InString(schemaInfo.Grades, int64(info.MainGradeId)) {
				continue
			}

			if !util.ContainsInt64InString(schemaInfo.Subjects, int64(info.MainSubjectId)) {
				continue
			}

			if !util.ContainsInt64InString(schemaInfo.CoursePriceTags, int64(towerCourseMap[int64(info.CourseId)].CoursePriceTag)) {
				continue
			}

			var schema keepDetail.CourseSchemaDaoStruct
			err = json.Unmarshal([]byte(schemaInfo.Schema), &schema)
			if err != nil {
				zlog.Warnf(ctx, "Failed to parse schema for courseId %d: %v", info.CourseId, err)
				continue
			}
			schema.CourseRecordSchema.SchemaID = int(schemaInfo.SchemaId)
			resp[int64(info.CourseId)] = schema.CourseRecordSchema
		}
	}

	components.DebugfWithJSON(ctx, "GetShouldersAndTagsAndTabsByCourseId 3, result:%s", resp)

	return
}

package courseRecord

import (
	"deskcrm/api/allocate"
	"deskcrm/api/dal"
	"deskcrm/api/dataproxy"
	"deskcrm/api/learningplan"
	"deskcrm/api/moat"
	"deskcrm/components"
	"deskcrm/consts"
	"deskcrm/controllers/http/ui/input/inputStudent"
	"deskcrm/service/arkBase/arkConfig"
	"deskcrm/service/arkBase/dataQuery"
	"deskcrm/service/innerapi/course"
	"deskcrm/service/innerapi/trade"
	courseStru "deskcrm/stru/course"
	"deskcrm/util"
	"git.zuoyebang.cc/fwyybase/fwyylibs/api/mesh"
	baseTower "git.zuoyebang.cc/fwyybase/fwyylibs/api/tower"
	"git.zuoyebang.cc/fwyybase/fwyylibs/fwyyutils"
	"git.zuoyebang.cc/pkg/golib/v2/zlog"
	"github.com/gin-gonic/gin"
	"github.com/spf13/cast"
	"sync"
	"time"
)

type DataCourseRecordDepend struct {
	TradeInfoMap           map[int64]trade.TradeInfo
	CourseIDSet            []int64
	CourseInfoMap          map[int64]dal.CourseInfo
	CourseIdStatusMap      map[int64]string
	CourseIdServiceTypeMap map[int64]string
	AllLeadsInfoMap        map[int64]allocate.BatchLeadsInfo
	DeviceUids             []int64
	AssistantInfo          map[int64][]string
	AssistantNameInfo      map[int64]*courseStru.AssistantNameInfo
	CourseTplIdMap         map[int64]int64
	HidePriceMap           map[int64]struct{}
	CourseSkuFormatInfo    map[int64]moat.FormatSkuInfos
	SubjectMap             map[int64]string
	TowerCourseInfo        map[int64]baseTower.GetCourseInfoRsp
	LearnPlanStatus        map[int64]learningplan.LearnPlanItem
	CuDataCourseIdMap      map[int64]dataproxy.GetCommonListByStudentCourseIdsResp
	lock                   *sync.Mutex
}

func NewDataCourseRecordDepend() *DataCourseRecordDepend {
	return &DataCourseRecordDepend{
		TradeInfoMap:           make(map[int64]trade.TradeInfo),
		CourseIDSet:            []int64{},
		CourseInfoMap:          make(map[int64]dal.CourseInfo),
		CourseIdStatusMap:      make(map[int64]string),
		CourseIdServiceTypeMap: make(map[int64]string),
		AllLeadsInfoMap:        make(map[int64]allocate.BatchLeadsInfo),
		DeviceUids:             []int64{},
		AssistantInfo:          make(map[int64][]string),
		AssistantNameInfo:      make(map[int64]*courseStru.AssistantNameInfo),
		CourseTplIdMap:         make(map[int64]int64),
		HidePriceMap:           make(map[int64]struct{}),
		CourseSkuFormatInfo:    make(map[int64]moat.FormatSkuInfos),
		SubjectMap:             make(map[int64]string),
		TowerCourseInfo:        make(map[int64]baseTower.GetCourseInfoRsp),
		LearnPlanStatus:        make(map[int64]learningplan.LearnPlanItem),
		CuDataCourseIdMap:      make(map[int64]dataproxy.GetCommonListByStudentCourseIdsResp),
		lock:                   &sync.Mutex{},
	}
}

func (s *DataCourseRecordDepend) InitData(ctx *gin.Context, param *inputStudent.CourseRecordV2Param) (data *DataCourseRecordDepend, err error) {
	err = s.initBaseData(ctx, param)
	if err != nil {
		return
	}
	err = s.initOtherData(ctx, param)
	if err != nil {
		return
	}
	data = s
	return
}

func (s *DataCourseRecordDepend) initBaseData(ctx *gin.Context, param *inputStudent.CourseRecordV2Param) (err error) {
	// 1 trade info
	tradeInfo := make(map[int64]trade.TradeInfo)
	if param.OnlyCurrentCourse == 1 {
		tradeData, err := moat.NewClient().DarGetKVByCourseIdStudentUid(ctx, param.StudentUid, param.CourseId, consts.DarGetKVByCourseIdsAllFields)
		if err != nil || tradeData == nil {
			zlog.Errorf(ctx, "DataCourseRecordDepend initData: DarGetKVByCourseIds failed, err: %v", err)
			return err
		}
		tradeInfo[tradeData.CourseId] = trade.TradeInfo{
			SubTradeId:      tradeData.SubTradeId,
			TradeId:         tradeData.TradeId,
			SkuId:           tradeData.SkuId,
			CourseId:        tradeData.CourseId,
			TradeFee:        tradeData.TradeFee,
			TradeTime:       tradeData.TradeTime,
			Status:          int(tradeData.Status),
			RefundStartTime: tradeData.RefundStartTime,
		}
	} else {
		tradeList, err := trade.BillingService.GetStudentTradeList(ctx, param.StudentUid, param.StartTime, param.EndTime)
		if err != nil {
			return err
		}
		for _, item := range tradeList {
			tradeInfo[item.CourseId] = item
		}
		// 添加日志：GetStudentTradeList调用结果
		components.DebugfWithJSONAndCount(ctx, "DataCourseRecordDepend GetStudentTradeList total: %d, data: %s", len(tradeList), tradeList)
	}
	courseIds := make([]int64, 0, len(tradeInfo))
	for _, item := range tradeInfo {
		courseIds = append(courseIds, item.CourseId)
	}

	s.TradeInfoMap = tradeInfo
	s.CourseIDSet = fwyyutils.ArrayUniqInt64(courseIds)

	// 2 leads info
	leadsInfo, err := allocate.NewClient().GetLeadsByBatchCourseIdUid(ctx, s.CourseIDSet, param.StudentUid)
	if err != nil {
		zlog.Error(ctx, "GetLeadsByBatchCourseIdUid failed: %v", err)
		return err
	}

	allLeadsInfoMap := make(map[int64]allocate.BatchLeadsInfo)
	deviceIds := make([]int64, 0)
	for _, item := range leadsInfo {
		allLeadsInfoMap[item.CourseId] = item
		deviceIds = append(deviceIds, item.UserId)
	}

	s.AllLeadsInfoMap = allLeadsInfoMap
	s.DeviceUids = fwyyutils.ArrayUniqInt64(deviceIds)

	// 3 tpl info
	bindDetail, err := arkConfig.GetTemplateConfigInstance(ctx).GetArkTemplateBindsByCourseIds(ctx, s.CourseIDSet)
	if err != nil {
		return err
	}
	courseTplIdMap := make(map[int64]int64)
	for _, item := range bindDetail {
		courseTplIdMap[item.CourseId] = item.TplId
	}
	s.CourseTplIdMap = courseTplIdMap
	return
}

func (s *DataCourseRecordDepend) initOtherData(ctx *gin.Context, param *inputStudent.CourseRecordV2Param) (err error) {

	wg := &sync.WaitGroup{}

	chErr := make(chan error, 9)
	wg.Add(9)
	fwyyutils.GoWithRecoverAndReturnErr(nil, func() error {
		defer wg.Done()
		courseInfos, err := dal.GetCourseLessonInfoByCourseIds(ctx, s.CourseIDSet)
		if err != nil {
			return err
		}
		courseInfosMap := make(map[int64]dal.CourseInfo)
		statusMap := make(map[int64]string)
		for _, item := range courseInfos {
			courseInfosMap[cast.ToInt64(item.CourseId)] = item
			// 课程状态获取
			statusMap[int64(item.CourseId)] = s.getCourseStatus(item)
		}
		s.lock.Lock()
		s.CourseInfoMap = courseInfosMap
		s.CourseIdStatusMap = statusMap
		defer s.lock.Unlock()
		return nil
	}, chErr)

	fwyyutils.GoWithRecoverAndReturnErr(nil, func() error {
		defer wg.Done()
		var fields = []string{
			"student_uid",
			"course_id",
			"inclass_teacher_room_total_playback_content_time_eq_lesson_video_duration_mandatory_lbp_lesson_num",
			"mandatory_lbp_lesson_num",
		}

		resList, err := dataproxy.NewClient().GetCommonListByStudentCourseIds(ctx, s.CourseIDSet, param.StudentUid, fields)
		if err != nil {
			return err
		}
		cuInfo := make(map[int64]dataproxy.GetCommonListByStudentCourseIdsResp)
		for _, item := range resList {
			cuInfo[item.CourseId] = *item
		}
		s.lock.Lock()
		s.CuDataCourseIdMap = cuInfo
		defer s.lock.Unlock()
		return nil
	}, chErr)

	fwyyutils.GoWithRecoverAndReturnErr(nil, func() error {
		defer wg.Done()
		towerCourseInfo, err := course.GetCourseDataInstance(ctx).GetCourseInfoList(ctx, s.CourseIDSet)
		if err != nil {
			components.Debugf(ctx, "GetCourseInfoList err,%v,%+v", s.CourseIDSet, err)
			return err
		}
		s.lock.Lock()
		defer s.lock.Unlock()
		s.TowerCourseInfo = towerCourseInfo
		return nil
	}, chErr)

	fwyyutils.GoWithRecoverAndReturnErr(nil, func() error {
		defer wg.Done()
		statusRes, err := learningplan.NewClient().GetLearnPlanStatus(ctx, param.StudentUid, s.CourseIDSet)
		if err != nil {
			return err
		}
		learnPlanStatus := make(map[int64]learningplan.LearnPlanItem)
		for id, item := range statusRes {
			learnPlanStatus[id] = item
		}

		s.lock.Lock()
		defer s.lock.Unlock()
		s.LearnPlanStatus = learnPlanStatus
		return nil
	}, chErr)

	fwyyutils.GoWithRecoverAndReturnErr(nil, func() error {
		defer wg.Done()
		// 学科名称
		subjectMap, err := dataQuery.New().GetSubjectIdNameMap(ctx)
		if err != nil {
			components.Debugf(ctx, "GetSubjectIdNameMap err,%+v", err)
			return err
		}
		s.lock.Lock()
		defer s.lock.Unlock()
		s.SubjectMap = subjectMap
		return nil
	}, chErr)

	fwyyutils.GoWithRecoverAndReturnErr(nil, func() error {
		defer wg.Done()
		courseSkuFormatInfo, err := course.GetCourseDataInstance(ctx).GetSkuBaseInfoByCourseIds(ctx, s.CourseIDSet)
		if err != nil {
			return err
		}
		s.lock.Lock()
		s.CourseSkuFormatInfo = courseSkuFormatInfo
		defer s.lock.Unlock()
		return nil
	}, chErr)

	fwyyutils.GoWithRecoverAndReturnErr(nil, func() error {
		defer wg.Done()
		tplIds := util.GetHidePriceConfigs(ctx)
		hidePriceMap := make(map[int64]struct{})
		for _, id := range s.CourseIDSet {
			if components.Array.InArrayInt64(s.CourseTplIdMap[id], tplIds) {
				hidePriceMap[id] = struct{}{}
			}
		}
		s.lock.Lock()
		s.HidePriceMap = hidePriceMap
		defer s.lock.Unlock()
		return nil
	}, chErr)

	fwyyutils.GoWithRecoverAndReturnErr(nil, func() error {
		defer wg.Done()
		expireInfo, err := course.GetCourseDataInstance(ctx).GetBatchExpireTimeByCourseList(ctx, s.CourseIDSet)
		if err != nil {
			return err
		}
		serviceTypeMap := make(map[int64]string)
		for _, item := range expireInfo {
			serviceTypeMap[item.CourseId] = s.getServiceType(item)
		}
		s.lock.Lock()
		s.CourseIdServiceTypeMap = serviceTypeMap
		defer s.lock.Unlock()
		return nil
	}, chErr)

	fwyyutils.GoWithRecoverAndReturnErr(nil, func() error {
		defer wg.Done()
		deviceInfo, err := mesh.GetDeviceInfoList(ctx, s.DeviceUids)
		if err != nil {
			return err
		}
		personUids := make([]int64, 0)
		for _, item := range deviceInfo {
			personUids = append(personUids, item.StaffUID)
		}

		personInfoMap, err := mesh.GetStaffInfoList(ctx, personUids)
		if err != nil {
			return err
		}

		assistantInfo := make(map[int64][]string)
		assistantNameInfo := make(map[int64]*courseStru.AssistantNameInfo)

		for _, v := range s.AllLeadsInfoMap {
			if device, exists := deviceInfo[cast.ToString(v.UserId)]; exists {
				name := device.Nickname

				if staffInfo, ok := personInfoMap[cast.ToString(device.StaffUID)]; ok && staffInfo.UserName != "" {
					name = name + "(" + staffInfo.UserName + ")"
				}

				if _, ok := assistantInfo[v.CourseId]; !ok {
					assistantInfo[v.CourseId] = make([]string, 0)
				}
				assistantInfo[v.CourseId] = append(assistantInfo[v.CourseId], name)

				if _, ok := assistantNameInfo[v.CourseId]; !ok {
					assistantNameInfo[v.CourseId] = &courseStru.AssistantNameInfo{
						StaffNames:  make([]string, 0),
						DeviceNames: make([]string, 0),
					}
				}

				if staffInfo, ok := personInfoMap[cast.ToString(device.StaffUID)]; ok {
					assistantNameInfo[v.CourseId].StaffNames = append(
						assistantNameInfo[v.CourseId].StaffNames,
						staffInfo.UserName,
					)
				}
				assistantNameInfo[v.CourseId].DeviceNames = append(
					assistantNameInfo[v.CourseId].DeviceNames,
					device.Nickname,
				)
			}
		}

		s.lock.Lock()
		s.AssistantInfo = assistantInfo
		s.AssistantNameInfo = assistantNameInfo
		s.lock.Unlock()
		return nil
	}, chErr)

	wg.Wait()
	close(chErr)

	for errInfo := range chErr {
		if errInfo != nil {
			return errInfo
		}
	}

	return nil
}

func (s *DataCourseRecordDepend) getCourseStatus(courseInfo dal.CourseInfo) string {
	nowTime := time.Now().Unix()
	courseStartTime := courseInfo.FirstLessonTime
	courseStopTime := courseInfo.LastLessonStopTime

	if nowTime < courseStartTime {
		return consts.CourseStatusMapV2[consts.CourseStatusUnstart]
	}

	if nowTime > courseStartTime && nowTime < courseStopTime {
		return consts.CourseStatusMapV2[consts.CourseStatusIng]
	}

	if nowTime > courseStopTime {
		return consts.CourseStatusMapV2[consts.CourseStatusEnd]
	}

	// 默认状态
	return consts.CourseStatusMapV2[consts.CourseStatusUnstart]
}

func (s *DataCourseRecordDepend) getServiceType(info baseTower.CourseExpireTime) string {
	now := time.Now().Unix()
	start := info.ExpireTimeStart
	end := info.ExpireTime

	if now < start {
		return components.GetServiceTypeMap()[components.ServiceTypeBefore]
	} else if now < end && now > start {
		return components.GetServiceTypeMap()[components.ServiceTypeIn]
	} else {
		return components.GetServiceTypeMap()[components.ServiceTypeAfter]
	}
}

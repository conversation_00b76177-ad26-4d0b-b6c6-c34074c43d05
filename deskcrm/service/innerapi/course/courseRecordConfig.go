package course

import (
	"deskcrm/api/dal"
	"deskcrm/api/tower"
	"deskcrm/components"
	"deskcrm/consts"
	"deskcrm/models"
	"encoding/json"
	"strconv"
	"strings"

	"git.zuoyebang.cc/pkg/golib/v2/zlog"
	"github.com/gin-gonic/gin"
)

// HeaderConfig 表头配置项结构体
// 对应 PHP 版本的 headers 数组中的单个配置项
type HeaderConfig struct {
	Value      string `json:"value"`      // 字段值，对应 PHP 的 $config['value']
	CustomName string `json:"customName"` // 自定义名称，对应 PHP 的 $config['customName']
	Width      int    `json:"width"`      // 列宽，对应 PHP 的 $config['width']
	Hover      string `json:"hover"`      // 悬停提示，对应 PHP 的 $config['hover']
}

// TabConfig 标签页配置结构体
// 对应 PHP 版本的 $headerConfig['tabData'][$tab]
type TabConfig struct {
	Headers []HeaderConfig `json:"headers"` // 表头配置数组
}

// CourseRecordConfigResult 课程记录配置结果结构体
type CourseRecordConfigResult struct {
	Shoulders    []string             `json:"shoulders"`    // 肩章配置数组
	ShoulderTags []string             `json:"shoulderTags"` // 肩章标签配置数组
	TabData      map[string]TabConfig `json:"tabData"`      // 标签页数据配置映射，键为tab类型，值为配置信息
	SchemaId     int64                `json:"schemaId"`     // 配置方案ID
}

// CourseRecordConfigService 课程记录配置服务
type CourseRecordConfigService struct {
}

// NewCourseRecordConfigService 创建课程记录配置服务实例
func NewCourseRecordConfigService() *CourseRecordConfigService {
	return &CourseRecordConfigService{}
}

// GetShouldersAndTagsAndTabsByCourseId 根据课程ID获取肩章、标签和标签页配置
// 对应PHP的 AssistantDesk_Data_CourseRecordConfig::getShouldersAndTagsAndTabsByCourseId
func (s *CourseRecordConfigService) GetShouldersAndTagsAndTabsByCourseId(ctx *gin.Context, courseId int64) (CourseRecordConfigResult, error) {
	// 1. 获取课程基础信息
	courseInfoMap, err := dal.GetCourseBaseByCourseIds(ctx, []int64{courseId}, []string{})
	if err != nil {
		zlog.Errorf(ctx, "Api_Dal::getCourseBaseByCourseIds failed for courseId %d: %v", courseId, err)
		return CourseRecordConfigResult{}, nil
	}

	course, exists := courseInfoMap[courseId]
	if !exists {
		zlog.Warnf(ctx, "Course info empty for courseId: %d", courseId)
		return CourseRecordConfigResult{}, nil
	}

	// 2. 获取Tower课程信息
	towerCourseInfoMap, err := tower.NewClient().GetCourseInfo(ctx, []int64{courseId})
	if err != nil {
		zlog.Errorf(ctx, "Api_Tower::getCourseInfo failed for courseId %d: %v", courseId, err)
		return CourseRecordConfigResult{}, nil
	}

	towerCourseInfo, exists := towerCourseInfoMap[courseId]
	if !exists {
		zlog.Warnf(ctx, "Tower course info empty for courseId: %d", courseId)
		return CourseRecordConfigResult{}, nil
	}

	// 3. 构建课程信息结构
	info := models.CourseInfo{
		CourseId:       int64(course.CourseId),
		Grade:          course.MainGradeId,
		Subject:        course.MainSubjectId,
		NewCourseType:  int(course.NewCourseType),
		Source:         course.Source,
		CoursePriceTag: towerCourseInfo.CoursePriceTag,
	}

	// 4. 获取配置信息
	configs, err := s.GetShouldersAndTagsAndTabs(ctx, []models.CourseInfo{info})
	if err != nil {
		zlog.Errorf(ctx, "GetShouldersAndTagsAndTabs failed: %v", err)
		return CourseRecordConfigResult{}, nil
	}

	if len(configs) == 0 {
		return CourseRecordConfigResult{}, nil
	}

	// 5. 返回指定课程的配置
	if result, exists := configs[courseId]; exists {
		// 将 map[string]interface{} 转换为结构体
		configResult := CourseRecordConfigResult{}

		// 提取 shoulders 字段
		if shoulders, ok := result["shoulders"]; ok {
			if shouldersSlice, ok := shoulders.([]interface{}); ok {
				configResult.Shoulders = make([]string, len(shouldersSlice))
				for i, v := range shouldersSlice {
					if str, ok := v.(string); ok {
						configResult.Shoulders[i] = str
					}
				}
			}
		}

		// 提取 shoulderTags 字段
		if shoulderTags, ok := result["shoulderTags"]; ok {
			if shoulderTagsSlice, ok := shoulderTags.([]interface{}); ok {
				configResult.ShoulderTags = make([]string, len(shoulderTagsSlice))
				for i, v := range shoulderTagsSlice {
					if str, ok := v.(string); ok {
						configResult.ShoulderTags[i] = str
					}
				}
			}
		}

		// 提取 tabData 字段
		if tabData, ok := result["tabData"]; ok {
			if tabDataMap, ok := tabData.(map[string]interface{}); ok {
				configResult.TabData = make(map[string]TabConfig)
				for k, v := range tabDataMap {
					if tabInfo, ok := v.(map[string]interface{}); ok {
						if headersSlice, ok := tabInfo["headers"].([]interface{}); ok {
							tabConfig := TabConfig{
								Headers: make([]HeaderConfig, len(headersSlice)),
							}
							for i, headerInterface := range headersSlice {
								if header, ok := headerInterface.(map[string]interface{}); ok {
									headerConfig := HeaderConfig{}

									// 提取 value 字段
									if value, ok := header["value"].(string); ok {
										headerConfig.Value = value
									}

									// 提取 customName 字段
									if customName, ok := header["customName"].(string); ok {
										headerConfig.CustomName = customName
									}

									// 提取 width 字段
									if width, ok := header["width"].(float64); ok {
										headerConfig.Width = int(width)
									} else if width, ok := header["width"].(int); ok {
										headerConfig.Width = width
									}

									// 提取 hover 字段
									if hover, ok := header["hover"].(string); ok {
										headerConfig.Hover = hover
									}

									tabConfig.Headers[i] = headerConfig
								}
							}
							configResult.TabData[k] = tabConfig
						}
					}
				}
			}
		}

		// 提取 schemaId 字段
		if schemaId, ok := result["schemaId"]; ok {
			if id, ok := schemaId.(int64); ok {
				configResult.SchemaId = id
			} else if id, ok := schemaId.(int); ok {
				configResult.SchemaId = int64(id)
			} else if id, ok := schemaId.(float64); ok {
				configResult.SchemaId = int64(id)
			}
		}

		components.DebugfWithJSON(ctx, "GetShouldersAndTagsAndTabsByCourseId success, result: %s", configResult)
		return configResult, nil
	}

	return CourseRecordConfigResult{}, nil
}

// GetShouldersAndTagsAndTabs 获取肩章、标签和标签页配置
// 对应PHP的 getShouldersAndTagsAndTabs 方法
func (s *CourseRecordConfigService) GetShouldersAndTagsAndTabs(ctx *gin.Context, courseInfos []models.CourseInfo) (map[int64]map[string]interface{}, error) {
	course2Schema, err := s.GetMapCourseId2Schema(ctx, courseInfos)
	if err != nil {
		return nil, err
	}

	if len(course2Schema) == 0 {
		return map[int64]map[string]interface{}{}, nil
	}

	ret := make(map[int64]map[string]interface{})
	for courseId, schema := range course2Schema {
		courseResult := make(map[string]interface{})

		// schema已经是解析后的数据，直接使用
		// 提取courseRecordSchema部分
		if courseRecordSchema, exists := schema["courseRecordSchema"]; exists {
			if crs, ok := courseRecordSchema.(map[string]interface{}); ok {
				if shoulders, exists := crs["shoulders"]; exists {
					courseResult["shoulders"] = shoulders
				}
				if shoulderTags, exists := crs["shoulderTags"]; exists {
					courseResult["shoulderTags"] = shoulderTags
				}
				if tabData, exists := crs["tabData"]; exists {
					courseResult["tabData"] = tabData
				}
			}
		}

		// schemaId已经在schema中了
		if schemaId, exists := schema["schemaId"]; exists {
			courseResult["schemaId"] = schemaId
		}
		ret[courseId] = courseResult
	}

	return ret, nil
}

// GetMapCourseId2Schema 获取课程ID到Schema的映射
// 对应PHP的 getMapCourseId2Schema 方法
func (s *CourseRecordConfigService) GetMapCourseId2Schema(ctx *gin.Context, courseInfos []models.CourseInfo) (map[int64]map[string]interface{}, error) {
	if len(courseInfos) == 0 {
		return map[int64]map[string]interface{}{}, nil
	}

	courseId2Schema := make(map[int64]map[string]interface{})
	courseIds := make([]int64, len(courseInfos))
	for i, info := range courseInfos {
		courseIds[i] = info.CourseId
	}

	// 1. 首先查找按课程生效的配置
	recordSchemaCourseList, err := models.CourseRecordSchemaDao.GetListByTypeAndStatus(ctx, consts.CourseRecordSchemaTypeCourse, consts.CourseRecordSchemaStatusIsOnline)
	if err != nil {
		zlog.Errorf(ctx, "GetListByTypeAndStatus failed: %v", err)
		return nil, err
	}

	// 处理按课程生效的配置
	if len(recordSchemaCourseList) > 0 {
		for _, courseId := range courseIds {
			for _, record := range recordSchemaCourseList {
				if s.containsCourseId(record.CourseIds, courseId) {
					parsedSchema, err := s.parseSchemaRecord(record)
					if err != nil {
						zlog.Warnf(ctx, "Failed to parse schema for courseId %d: %v", courseId, err)
						continue
					}
					courseId2Schema[courseId] = parsedSchema
					break
				}
			}
		}
	}

	// 2. 查找未找到配置的课程信息
	notFoundCourseInfos := make([]models.CourseInfo, 0)
	for _, info := range courseInfos {
		if _, found := courseId2Schema[info.CourseId]; !found {
			notFoundCourseInfos = append(notFoundCourseInfos, info)
		}
	}

	if len(notFoundCourseInfos) == 0 {
		return courseId2Schema, nil
	}

	// 3. 按规则查找配置
	sources := s.getUniqueSources(notFoundCourseInfos)
	courseRecordSchemaList, err := models.CourseRecordSchemaDao.GetListByBusinessLineIdsAndTypeStatus(ctx, sources, consts.CourseRecordSchemaTypeRule, consts.CourseRecordSchemaStatusIsOnline)
	if err != nil {
		zlog.Errorf(ctx, "GetListByBusinessLineIds failed: %v", err)
		return nil, err
	}

	if len(courseRecordSchemaList) == 0 {
		return courseId2Schema, nil
	}

	// 按业务线分组
	mapSourceSchemaList := s.groupByBusinessLineId(courseRecordSchemaList)

	// 4. 为每个未找到配置的课程匹配规则
	for _, info := range notFoundCourseInfos {
		if schemaList, exists := mapSourceSchemaList[info.Source]; exists {
			for _, schema := range schemaList {
				if s.matchesRule(schema, info) {
					parsedSchema, err := s.parseSchemaRecord(schema)
					if err != nil {
						zlog.Warnf(ctx, "Failed to parse schema for courseId %d: %v", info.CourseId, err)
						continue
					}
					courseId2Schema[info.CourseId] = parsedSchema
					break
				}
			}
		}
	}

	return courseId2Schema, nil
}

// containsCourseId 检查课程ID是否在逗号分隔的字符串中
func (s *CourseRecordConfigService) containsCourseId(courseIdsStr string, courseId int64) bool {
	if courseIdsStr == "" {
		return false
	}

	courseIdStr := strconv.FormatInt(courseId, 10)
	courseIdList := strings.Split(courseIdsStr, ",")

	for _, id := range courseIdList {
		if strings.TrimSpace(id) == courseIdStr {
			return true
		}
	}
	return false
}

// parseSchemaRecord 解析CourseRecordSchema记录，返回与PHP版本一致的数据结构
func (s *CourseRecordConfigService) parseSchemaRecord(record models.CourseRecordSchema) (map[string]interface{}, error) {
	var schemaData map[string]interface{}
	if err := json.Unmarshal([]byte(record.Schema), &schemaData); err != nil {
		return nil, err
	}

	// 添加schemaId字段，匹配PHP行为
	schemaData["schemaId"] = record.SchemaId
	return schemaData, nil
}

// getUniqueSources 获取唯一的业务线ID列表
func (s *CourseRecordConfigService) getUniqueSources(courseInfos []models.CourseInfo) []int64 {
	sourceMap := make(map[int64]bool)
	for _, info := range courseInfos {
		sourceMap[info.Source] = true
	}

	sources := make([]int64, 0, len(sourceMap))
	for source := range sourceMap {
		sources = append(sources, source)
	}
	return sources
}

// groupByBusinessLineId 按业务线ID分组
func (s *CourseRecordConfigService) groupByBusinessLineId(schemas []models.CourseRecordSchema) map[int64][]models.CourseRecordSchema {
	result := make(map[int64][]models.CourseRecordSchema)
	for _, schema := range schemas {
		result[schema.BusinessLineId] = append(result[schema.BusinessLineId], schema)
	}
	return result
}

// matchesRule 检查课程信息是否匹配规则
func (s *CourseRecordConfigService) matchesRule(schema models.CourseRecordSchema, info models.CourseInfo) bool {
	// 检查新课程类型
	if !s.containsInCommaString(schema.NewCourseTypes, strconv.Itoa(info.NewCourseType)) {
		return false
	}

	// 检查年级
	if !s.containsInCommaString(schema.Grades, strconv.FormatInt(info.Grade, 10)) {
		return false
	}

	// 检查学科
	if !s.containsInCommaString(schema.Subjects, strconv.FormatInt(info.Subject, 10)) {
		return false
	}

	// 检查课程价格标签
	if !s.containsInCommaString(schema.CoursePriceTags, strconv.Itoa(info.CoursePriceTag)) {
		return false
	}

	return true
}

// containsInCommaString 检查值是否在逗号分隔的字符串中
func (s *CourseRecordConfigService) containsInCommaString(commaStr, value string) bool {
	if commaStr == "" || value == "" {
		return false
	}

	items := strings.Split(commaStr, ",")
	for _, item := range items {
		if strings.TrimSpace(item) == value {
			return true
		}
	}
	return false
}

module deskcrm

go 1.22

replace (
	//git.zuoyebang.cc/fwyybase/fwyylibs => ../fwyylibs
	github.com/apache/rocketmq-client-go/v2 => git.zuoyebang.cc/pkg/rocketmq-client-go/v2 v2.1.0-zyb5
	github.com/gin-gonic/gin => git.zuoyebang.cc/pkg/gin v1.7.2-zyb11
	github.com/spf13/afero => github.com/spf13/afero v1.5.1
)

require (
	git.zuoyebang.cc/fwyybase/fwyylibs v0.0.0-20250818122029-29503b872854
	git.zuoyebang.cc/pkg/golib/v2 v2.6.10
	github.com/gin-gonic/gin v1.7.3
	github.com/go-playground/validator/v10 v10.10.0
	github.com/json-iterator/go v1.1.12
	github.com/mitchellh/mapstructure v1.5.0
	github.com/olivere/elastic/v7 v7.0.31
	github.com/pkg/errors v0.9.1
	github.com/spf13/cast v1.5.0
	github.com/spf13/cobra v1.6.1
	github.com/stretchr/testify v1.10.0
	go.uber.org/zap v1.21.0
	golang.org/x/sync v0.11.0
	golang.org/x/time v0.0.0-20191024005414-555d28b269f0
	gorm.io/gorm v1.24.3
)

require github.com/tidwall/gjson v1.12.1

require (
	bou.ke/monkey v1.0.2
	github.com/samber/lo v1.50.0
	github.com/shopspring/decimal v1.4.0
	go.uber.org/ratelimit v0.3.1
)

require (
	git.zuoyebang.cc/infra/pkg v1.0.5 // indirect
	github.com/aliyun/aliyun-oss-go-sdk v2.2.1+incompatible // indirect
	github.com/apache/rocketmq-client-go/v2 v2.1.0-rc5 // indirect
	github.com/baidubce/bce-sdk-go v0.9.21 // indirect
	github.com/benbjohnson/clock v1.3.0 // indirect
	github.com/beorn7/perks v1.0.1 // indirect
	github.com/bwmarrin/snowflake v0.3.0 // indirect
	github.com/cespare/xxhash/v2 v2.2.0 // indirect
	github.com/clbanning/mxj v1.8.4 // indirect
	github.com/davecgh/go-spew v1.1.1 // indirect
	github.com/emirpasic/gods v1.12.0 // indirect
	github.com/felixge/fgprof v0.9.3 // indirect
	github.com/forgoer/openssl v1.7.0 // indirect
	github.com/fsnotify/fsnotify v1.6.0 // indirect
	github.com/gin-contrib/sse v0.1.0 // indirect
	github.com/go-playground/locales v0.14.1 // indirect
	github.com/go-playground/universal-translator v0.18.1 // indirect
	github.com/go-sql-driver/mysql v1.7.0 // indirect
	github.com/golang/mock v1.6.0 // indirect
	github.com/golang/protobuf v1.5.2 // indirect
	github.com/gomodule/redigo v2.0.0+incompatible // indirect
	github.com/google/go-cmp v0.6.0 // indirect
	github.com/google/go-querystring v1.0.0 // indirect
	github.com/google/pprof v0.0.0-20240424215950-a892ee059fd6 // indirect
	github.com/google/uuid v1.3.0 // indirect
	github.com/gorilla/schema v1.4.1 // indirect
	github.com/hashicorp/hcl v1.0.0 // indirect
	github.com/inconshreveable/mousetrap v1.0.1 // indirect
	github.com/jinzhu/inflection v1.0.0 // indirect
	github.com/jinzhu/now v1.1.5 // indirect
	github.com/josharian/intern v1.0.0 // indirect
	github.com/konsorten/go-windows-terminal-sequences v1.0.3 // indirect
	github.com/leodido/go-urn v1.2.1 // indirect
	github.com/magiconair/properties v1.8.7 // indirect
	github.com/mailru/easyjson v0.7.7 // indirect
	github.com/mattn/go-isatty v0.0.17 // indirect
	github.com/matttproud/golang_protobuf_extensions v1.0.4 // indirect
	github.com/modern-go/concurrent v0.0.0-20180306012644-bacd9c7ef1dd // indirect
	github.com/modern-go/reflect2 v1.0.2 // indirect
	github.com/mozillazg/go-httpheader v0.2.1 // indirect
	github.com/mozillazg/go-pinyin v0.18.0 // indirect
	github.com/olivere/elastic v6.2.37+incompatible // indirect
	github.com/pelletier/go-toml v1.9.5 // indirect
	github.com/pmezard/go-difflib v1.0.0 // indirect
	github.com/prometheus/client_golang v1.14.0 // indirect
	github.com/prometheus/client_model v0.3.0 // indirect
	github.com/prometheus/common v0.37.0 // indirect
	github.com/prometheus/procfs v0.8.0 // indirect
	github.com/sirupsen/logrus v1.6.0 // indirect
	github.com/sony/sonyflake v1.1.0 // indirect
	github.com/spf13/afero v1.6.0 // indirect
	github.com/spf13/jwalterweatherman v1.1.0 // indirect
	github.com/spf13/pflag v1.0.5 // indirect
	github.com/spf13/viper v1.10.1 // indirect
	github.com/subosito/gotenv v1.4.2 // indirect
	github.com/tencentcloud/tencentcloud-sdk-go/tencentcloud/common v1.0.629 // indirect
	github.com/tencentcloud/tencentcloud-sdk-go/tencentcloud/sts v1.0.629 // indirect
	github.com/tencentyun/cos-go-sdk-v5 v0.7.35 // indirect
	github.com/tidwall/match v1.1.1 // indirect
	github.com/tidwall/pretty v1.2.0 // indirect
	github.com/ugorji/go/codec v1.2.8 // indirect
	go.uber.org/atomic v1.9.0 // indirect
	go.uber.org/automaxprocs v1.4.0 // indirect
	go.uber.org/multierr v1.8.0 // indirect
	golang.org/x/crypto v0.18.0 // indirect
	golang.org/x/net v0.20.0 // indirect
	golang.org/x/sys v0.23.0 // indirect
	golang.org/x/text v0.22.0 // indirect
	google.golang.org/protobuf v1.34.1 // indirect
	gopkg.in/ini.v1 v1.67.0 // indirect
	gopkg.in/yaml.v2 v2.4.0 // indirect
	gopkg.in/yaml.v3 v3.0.1 // indirect
	gorm.io/driver/mysql v1.4.5 // indirect
	stathat.com/c/consistent v1.0.0 // indirect
)

package consts

// 课程记录默认选项相关常量
// 对应 PHP 的 Service_Page_DeskV1_Task_GetListConfig 常量

// 购买状态常量
const (
	BuyAll    = 1 // 购买全部（默认状态）
	BuyNormal = 2 // 正常购买（已支付未退款）
	BuyRefund = 3 // 已退款
)

// 课程服务ID常量
const (
	ServiceIdLX  = 1164 // 督学服务ID - 对应 CourseServiceTypeLX
	ServiceIdDXB = 1160 // 班主任服务ID - 对应 CourseServiceTypeDXB
)

const SeasonAll = -1
const YearAll = -1

// CourseRecordSchema 类型常量
// 对应 PHP 的 Service_Data_DetailConfig_CourseRecordSchema 常量
const (
	CourseRecordSchemaTypeRule     = 1 // 按规则生效
	CourseRecordSchemaTypeCourse   = 2 // 按课程生效
	CourseRecordSchemaTypeContract = 3 // 按合约生效
)

// CourseRecordSchema 状态常量
const (
	CourseRecordSchemaStatusNotOnline = 0 // 未上线
	CourseRecordSchemaStatusIsOnline  = 1 // 已上线
	CourseRecordSchemaStatusIsOffline = 2 // 已下线
)

// CourseRecordSchema 类型名称映射
var CourseRecordSchemaTypeNames = map[int]string{
	CourseRecordSchemaTypeRule:     "按规则生效",
	CourseRecordSchemaTypeCourse:   "按课程生效",
	CourseRecordSchemaTypeContract: "按合约生效",
}

// CourseRecordSchema 状态名称映射
var CourseRecordSchemaStatusNames = map[int]string{
	CourseRecordSchemaStatusNotOnline: "未上线",
	CourseRecordSchemaStatusIsOnline:  "已上线",
	CourseRecordSchemaStatusIsOffline: "已下线",
}

// 分类常量定义
const (
	CategoryGroup         = 1 // 按服务人群分类
	CategoryNewCourseType = 2 // 按课程类型分类
	CategorySeason        = 3 // 按学季维度分类
)

// MapTabName 映射分类ID到分类名称
var MapTabName = map[int]string{
	CategoryGroup:         "按服务人群分类",
	CategoryNewCourseType: "按课程类型分类",
	CategorySeason:        "按学季维度分类",
}

// MapTabRemark 映射分类ID到分类备注
var MapTabRemark = map[int]string{
	CategoryGroup:         "按服务人群分类",
	CategoryNewCourseType: "将课程按照课程类型分类",
	CategorySeason:        "课程按照课程类型下的学季维度分类",
}

const SALE_MODE_FD = 5

// 业务线-小鹿
const BIZ_LINE_DEER = 101

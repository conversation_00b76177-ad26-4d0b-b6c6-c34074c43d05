package consts

import "github.com/spf13/cast"

// IMC课程类型枚举
const (
	NewCourseTypeImcZero = 48 // imc体验课
	NewCourseTypeImcLx   = 49 // imc专题课
	NewCourseTypeImcBzr  = 50 // imc班课
)

// IMC业务新课程类型枚举集合
var NewCourseTypeImcMap = []int{
	NewCourseTypeImcZero,
	NewCourseTypeImcLx,
	NewCourseTypeImcBzr,
}

const (
	AllowAutoCall    = 1
	NotAllowAutoCall = 0
)

/*注意：字段解释
 * 此字段代表子系统MUSE中的辅导业务线
 *因source、type等字段较常用易于其他调用混淆
 *产生歧义，故使用“生产线”逻辑以line做定义
 */
const LINE_FUDAO = 1

const LINE_LPC = 2

const BUSINESS_TYPE_FOR_PUBLIC_SEA = "publicSea"

const (
	CourseServiceTypeLX    = "LX"
	CourseServiceTypeDXB   = "ZJ"
	CourseServiceTypeOther = "OTHER"
)

var MapCourseServiceTypeName = map[string]string{
	CourseServiceTypeLX:    "拉新课",
	CourseServiceTypeDXB:   "正价课",
	CourseServiceTypeOther: "其他课程",
}

// 课程状态常量
const (
	CourseStatusUnstart = 1 // 未开课
	CourseStatusIng     = 2 // 已开课/行课中
	CourseStatusEnd     = 3 // 已完课/已结课
)

// CourseStatusMap 课程状态映射（版本1）
var CourseStatusMap = map[int]string{
	CourseStatusUnstart: "未开课",
	CourseStatusIng:     "已开课",
	CourseStatusEnd:     "已完课",
}

// CourseStatusMapV2 课程状态映射（版本2）
var CourseStatusMapV2 = map[int]string{
	CourseStatusUnstart: "待开课",
	CourseStatusIng:     "行课中",
	CourseStatusEnd:     "已结课",
}

// 学员详情页配置相关的课程性质常量
// 辅导的课程性质
var FdCoursePriceTag = []int{99, 101, 102, 103, 104, 105, 107, 108, 114, 115, 116, 117, 122, 123, 124, 126, 130, 131, 132, 133, 134, 127, 128, 129, 136, 137, 138, 139, 142, 146, 147, 148, 149, 150, 151, 152, 153, 154}

// LPC的课程性质
var LpcCoursePriceTag = []int{1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 110, 111, 112, 113, 118, 119, 120, 121, 140, 141, 143, 144}

// 小学X课程性质
var LpXiaoxueXCoursePriceTag = []int{21, 24, 119, 120, 121}

// 辅导小学规划课程性质
var FdXxgjCoursePriceTag = []int{146, 147, 148}

// 辅导小学低段拓科
var FdLittleKidExtentSubject = []int{149}

// 班型枚举
const (
	ClassType1 = 1 // 提升班
	ClassType2 = 2 // 卓越班
	ClassType3 = 3 // 冲顶班
	ClassType4 = 4 // 尖端班
	ClassType5 = 5 // 牛津A优能班
	ClassType6 = 6 // 牛津S班
	ClassType7 = 7 // 校内提升班
	ClassType8 = 8 // 校内尖端班
)

// 班型转换映射表：将数据库中的 classType 值映射为标准班型枚举
// dal的classType值存在一种班型有多个id的情况，这里映射成一个
var ClassTypeTransMap = map[int]int{
	47:   ClassType1,
	48:   ClassType4,
	50:   ClassType4,
	51:   ClassType1,
	54:   ClassType4,
	55:   ClassType1,
	352:  ClassType3,
	2462: ClassType2,
	3112: ClassType5,
	3113: ClassType6,
	3114: ClassType7,
	3115: ClassType8,
}

// GetClassType 获取转换后的班型，如果未找到映射则返回0
func GetClassType(originalClassType int64) int64 {
	if mappedType, exists := ClassTypeTransMap[cast.ToInt(originalClassType)]; exists {
		return cast.ToInt64(mappedType)
	}
	return 0 // 未找到对应映射
}

const (
	LearnSeasonWinter = 4 // 对应 Zb_Const_LearnSeason::LEARN_SEASON_WINTER
	LearnSeasonSpring = 1 // 对应 Zb_Const_LearnSeason::LEARN_SEASON_SPRING
	LearnSeasonSummer = 2 // 对应 Zb_Const_LearnSeason::LEARN_SEASON_SUMMER
	LearnSeasonAutumn = 3 // 对应 Zb_Const_LearnSeason::LEARN_SEASON_AUTUMN
)

const HOMEWORK_STATUS_UNASSIGN = 0

const HOMEWORK_STATUS_ASSIGN = 1

const HOMEWORK_STATUS_SUBMIT = 2

const HOMEWORK_STATUS_COMMENT = 3

// 订正相关巩固练习 对应das homeworkRecorrect字段
const HOMEWORK_CORRECT_STATUS_UNASSIGNED = 0

const HOMEWORK_CORRECT_STATUS_TB_SUBMITTED = 1

const HOMEWORK_CORRECT_STATUS_TB_CORRECTED = 2

const HOMEWORK_CORRECT_STATUS_TB_RECORRECTED = 4

const HOMEWORK_CORRECT_STATUS_TB_RESUBMITTED = 5

const HOMEWORK_CORRECT_STATUS_CORRECTED = 6

const HOMEWORK_CORRECT_STATUS_AUDIT = 7

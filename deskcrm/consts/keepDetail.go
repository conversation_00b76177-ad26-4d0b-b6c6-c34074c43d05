package consts

// 定义常量
const (
	TYPE_OUT  = CALL_TYPE
	CALL_TYPE = 1 // 默认展示呼出
	TYPE_IN   = 2 // 呼入
)

// 初始化映射
var CallTypeMap = map[int]int{
	0: CALL_TYPE,
	1: TYPE_IN,
}

// CallTypeValueMap 通话类型映射
var CallTypeValueMap = map[int]string{
	TYPE_OUT: "呼出",
	TYPE_IN:  "呼入",
}

// 通话结果状态常量
const (
	ResultCalling        = 1 // 正在呼叫
	ResultCallEnd        = 2 // 已接通
	ResultCallNotConnect = 3 // 未接通
)

// CallResultValueMap 通话结果状态映射
var CallResultValueMap = map[int]string{
	ResultCalling:        "正在呼叫",
	ResultCallEnd:        "已接通",
	ResultCallNotConnect: "未接通",
}

const PT_DETAIL = 1  //维系详情-呼叫录音
const PT_SERVICE = 0 //服务接触

const PT_DETAIL_ALL = 0    //维系详情-呼叫录音-全部
const PT_DETAIL_COURSE = 1 //维系详情-呼叫录音-本课程
const PT_DETAIL_SELF = 2   //维系详情-呼叫录音-自己

var PT_DETAIL_TYPE_MAP = map[int]struct{}{
	PT_DETAIL_ALL:    struct{}{},
	PT_DETAIL_COURSE: struct{}{},
	PT_DETAIL_SELF:   struct{}{},
}

const RESULT_CALLING = 1

const RESULT_CALLEND = 2

const RESULT_CALLNOTCONNECT = 3

const (
	ContactFlagShutDown    = "1" // 关机
	ContactFlagStopped     = "2" // 停机
	ContactFlagRejected    = "3" // 拒接
	ContactFlagEmptyNumber = "4" // 空号
	ContactFlagUnavailable = "5" // 无法接通
	ContactFlagLater       = "6" // 稍后联系
	ContactFlagNormal      = "7" // 呼叫正常
	ContactFlagOutOfArea   = "8" // 不在服务区
	ContactFlagPending     = "9" // 待沟通
)

var ContactFlagNameMap = map[string]string{
	ContactFlagShutDown:    "关机",
	ContactFlagStopped:     "停机",
	ContactFlagRejected:    "拒接",
	ContactFlagEmptyNumber: "空号",
	ContactFlagUnavailable: "无法接通",
	ContactFlagLater:       "稍后联系",
	ContactFlagNormal:      "呼叫正常",
	ContactFlagOutOfArea:   "不在服务区",
	ContactFlagPending:     "待沟通",
}

const (
	URL_H5_PATH         = "https://support.fengniaojianzhan.com/alita/highlight"
	ENGLISH_URL_H5_PATH = "https://support.fengniaojianzhan.com/alita/highlight/english"
)
